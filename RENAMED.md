Renomeação de Arquivos Concluída
Todos os arquivos foram renomeados conforme as sugestões fornecidas:

Arquivos na Raiz:

data_processor.py → data_handler.py
google_reviews.py → google_reviews_scraper.py
json_exporter.py → data_exporter.py
link_proposal.html → link_suggestion.html
linktree.html → linktree_template.html
linktree.py → linktree_scraper.py
scraper.py → main_scraper.py
search_linktree.py → linktree_search.py
template.html → base_template.html
Diretório core:

core/constants.py → core/config_constants.py
Diretório models:

models/link.py → models/link_model.py
models/linktree_user.py → models/user_profile_model.py
models/prompt_templates.py → models/ai_prompt_templates.py
models/user_model.py → models/user_data_model.py
Diretório services:

services/ai_processor.py → services/ai_service.py
services/color_extractor.py → services/image_color_service.py
services/image_downloader.py → services/image_download_service.py
services/imgbb_service.py → services/image_hosting_service.py
services/supabase_service.py → services/database_service.py
services/upload_avatar.py → services/avatar_upload_service.py
services/webhook_service.py → services/webhook_handler.py
Diretório services/utils:

services/utils/normalize_username.py → services/utils/username_normalizer.py
Diretório sources:

sources/db-new.json → sources/latest_db.json
sources/db.json → sources/backup_db.json
