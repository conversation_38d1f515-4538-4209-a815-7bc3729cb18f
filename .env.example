# LinkTree Scraper API Configuration
# Copie este arquivo para .env e ajuste as configurações conforme necessário

# API Configuration
LINKTREE_APP_NAME="LinkTree Scraper API"
LINKTREE_APP_VERSION="1.0.0"
LINKTREE_DEBUG=false

# Server Configuration
LINKTREE_HOST=0.0.0.0
LINKTREE_PORT=8000

# Rate Limiting
LINKTREE_RATE_LIMIT_REQUESTS=100
LINKTREE_RATE_LIMIT_WINDOW=60

# Redis Configuration (for rate limiting - opcional)
# LINKTREE_REDIS_URL=redis://localhost:6379/0
# LINKTREE_REDIS_HOST=localhost
# LINKTREE_REDIS_PORT=6379
# LINKTREE_REDIS_DB=0
# LINKTREE_REDIS_PASSWORD=

# Scraper Configuration
LINKTREE_SCRAPER_TIMEOUT=30
LINKTREE_SCRAPER_MAX_RETRIES=3
LINKTREE_SCRAPER_DELAY=1.0

# CORS Configuration (separar com vírgulas)
# LINKTREE_CORS_ORIGINS=["*"]
# LINKTREE_CORS_METHODS=["GET", "POST"]
# LINKTREE_CORS_HEADERS=["*"]
