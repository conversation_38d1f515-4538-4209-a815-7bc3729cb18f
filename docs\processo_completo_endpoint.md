# Endpoint de Processo Completo

## Descrição

O endpoint `/api/v1/scrape/process` foi criado para realizar todo o processo completo de scraping e upload para Firebase, equivalente ao comando:

```bash
python linktree.py USERNAME
```

## Funcionalidades

Este endpoint executa automaticamente todas as etapas do processo:

1. **Extração dos dados do usuário** - Faz scraping do perfil do Linktree
2. **Upload da imagem de perfil** - Envia avatar para ImgBB se disponível
3. **Geração do JSON unificado** - Cria arquivo JSON com todos os dados
4. **Processamento com IA** - Otimiza dados usando IA para formato de link page
5. **Upload para Firebase** - Envia dados processados para Firebase

## Endpoint

```
POST /api/v1/scrape/process
```

## Parâmetros

### Body (JSON)

```json
{
  "username": "nome_do_usuario",  // Opcional se URL for fornecida
  "url": "https://linktr.ee/nome_do_usuario"  // Opcional se username for fornecido
}
```

**Nota:** Pelo menos um dos campos (`username` ou `url`) deve ser fornecido.

## Exemplos de Uso

### 1. Usando apenas username

```bash
curl -X POST "http://localhost:8000/api/v1/scrape/process" \
  -H "Content-Type: application/json" \
  -d '{"username": "elonmusk"}'
```

### 2. Usando apenas URL

```bash
curl -X POST "http://localhost:8000/api/v1/scrape/process" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://linktr.ee/elonmusk"}'
```

### 3. Usando ambos (username e URL)

```bash
curl -X POST "http://localhost:8000/api/v1/scrape/process" \
  -H "Content-Type: application/json" \
  -d '{"username": "elonmusk", "url": "https://linktr.ee/elonmusk"}'
```

## Resposta

### Sucesso (Status 200)

```json
{
  "success": true,
  "message": "Processo completo realizado com sucesso. Dados processados e enviados para Firebase.",
  "data": {
    "username": "elonmusk",
    "url": "https://linktr.ee/elonmusk",
    "avatar_image": "https://i.ibb.co/...",
    "id": 12345,
    "tier": "free",
    "isActive": true,
    "description": "CEO of Tesla, SpaceX",
    "createdAt": 1234567890,
    "updatedAt": 1234567890,
    "number_of_links": 5,
    "links": [
      {
        "url": "https://tesla.com",
        "button_text": "Tesla"
      }
    ]
  },
  "firebase_url": "https://firebase.com/users/elonmusk.json",
  "timestamp": "2025-01-07T18:10:00Z"
}
```

### Erro (Status 400/500)

```json
{
  "success": false,
  "message": "Perfil 'usuario_inexistente' não encontrado no Linktree. Verifique se o nome de usuário está correto e se o perfil existe.",
  "data": null,
  "firebase_url": null,
  "timestamp": "2025-01-07T18:10:00Z"
}
```

## Possíveis Erros

### 1. Validação
- **400**: Username ou URL inválidos
- **400**: Nenhum campo fornecido

### 2. Perfil não encontrado
- **200**: `success: false` - Perfil não existe no Linktree

### 3. Erro de rede
- **200**: `success: false` - Problemas de conectividade

### 4. Erro no Firebase
- **200**: `success: false` - Problemas no upload para Firebase

### 5. Erro interno
- **500**: Erro não tratado no servidor

## Diferenças do Endpoint Simples

| Característica | `/scrape/user` | `/scrape/process` |
|----------------|----------------|-------------------|
| Extração de dados | ✅ | ✅ |
| Upload de imagem | ❌ | ✅ |
| JSON unificado | ❌ | ✅ |
| Processamento IA | ❌ | ✅ |
| Upload Firebase | ❌ | ✅ |
| Salva arquivos locais | ❌ | ✅ |
| Tempo de execução | ~2-5s | ~10-30s |

## Monitoramento

O endpoint incrementa automaticamente as métricas de scraping que podem ser consultadas em:

```
GET /api/v1/health/metrics
```

## Logs

O processo gera logs detalhados que incluem:
- Início do processo para o usuário
- Progresso de cada etapa
- Erros específicos se ocorrerem
- Sucesso com URL final do Firebase

## Arquivos Gerados

O processo cria os seguintes arquivos locais:

```
output/
├── nome_usuario/
│   ├── nome_usuario.json          # JSON original
│   ├── nome_usuario_avatar.jpg    # Avatar baixado
│   └── ...outros arquivos
└── nome_usuario_normalizado/
    └── nome_usuario_normalizado.json  # JSON unificado
```

## Considerações

1. **Tempo de execução**: O processo completo pode levar 10-30 segundos
2. **Rate limiting**: Respeite os limites do Linktree
3. **Armazenamento**: Arquivos são salvos localmente na pasta `output/`
4. **Firebase**: URL final disponível na resposta para consulta direta
5. **Imagens**: Avatares são enviados para ImgBB automaticamente
