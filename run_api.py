#!/usr/bin/env python3
"""
Script para iniciar a aplicação FastAPI do LinkTree Scraper
"""

import uvicorn
import sys
from pathlib import Path

# Adicionar o diretório raiz ao Python path
sys.path.insert(0, str(Path(__file__).parent))

from api.core.config import settings


def main():
    """Função principal para iniciar o servidor"""

    print(f"🚀 Iniciando {settings.app_name} v{settings.app_version}")
    print(f"📡 Servidor rodando em: http://{settings.host}:{settings.port}")
    print("📡 Servidor rodando em debug: http://127.0.0.1:8000/docs")
    print(f"📖 Documentação disponível em: http://{settings.host}:{settings.port}/docs")
    print(f"🔧 Modo debug: {'Ativado' if settings.debug else 'Desativado'}")
    print("-" * 60)

    uvicorn.run(
        "api.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
    )


if __name__ == "__main__":
    main()
