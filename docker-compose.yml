version: '3.8'

services:
  linktree-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: linktree-scraper-api
    ports:
      - "8000:8000"
    environment:
      - LINKTREE_HOST=0.0.0.0
      - LINKTREE_PORT=8000
      - LINKTREE_DEBUG=false
      - LINKTREE_APP_NAME=LinkTree Scraper API
      - LINKTREE_APP_VERSION=1.0.0
    env_file:
      - .env
    volumes:
      - ./output:/app/output
    restart: unless-stopped
    depends_on:
      - redis
    networks:
      - linktree-network

  redis:
    image: redis:7-alpine
    container_name: linktree-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - linktree-network
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    container_name: linktree-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - linktree-api
    restart: unless-stopped
    networks:
      - linktree-network

volumes:
  redis_data:

networks:
  linktree-network:
    driver: bridge
