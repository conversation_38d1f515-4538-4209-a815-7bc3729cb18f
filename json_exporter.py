# import json
# from pathlib import Path
# import warnings
# from models.linktree_user import Link<PERSON>User
# from services.color_extractor import ColorExtractor
# from services.image_downloader import download_image

# # Suppress PIL warnings about corrupt EXIF data
# warnings.filterwarnings("ignore", category=UserWarning, module="PIL")
# import os

# ONE_MINUTE = 60
# MAX_CALLS_PER_MINUTE = 30


# class JSONExporter:
#     def __init__(self, output_dir: str = "output"):
#         self.output_dir = output_dir
#         Path(self.output_dir).mkdir(parents=True, exist_ok=True)

#     def export_user_info(self, user: LinktreeUser):
#         user_dir = Path(self.output_dir) / user.username
#         user_dir.mkdir(parents=True, exist_ok=True)

#         # Create assets directory
#         assets_dir = user_dir / "assets"
#         assets_dir.mkdir(exist_ok=True)

#         # Download and save avatar image
#         local_avatar_path = None
#         if user.avartar_image:
#             image_name = os.path.basename(user.avartar_image)
#             local_avatar_path = download_image(
#                 user.avartar_image, assets_dir, user.username
#             )

#         user_info = {
#             "username": user.username,
#             "linktree_url": user.url,
#             "number_of_links": len(user.links),
#             "main_colors": (
#                 ColorExtractor().get_main_colors(user.avartar_image)
#                 if user.avartar_image
#                 else []
#             ),
#             "avatar_image": (
#                 local_avatar_path if local_avatar_path else user.avartar_image
#             ),
#             "tier": user.tier,
#             "isActive": user.isActive,
#             "description": user.description,
#             "createdAt": user.createdAt,
#             "updatedAt": user.updatedAt,
#         }

#         with open(user_dir / "user_info.json", "w", encoding="utf-8") as f:
#             json.dump(user_info, f, indent=2, ensure_ascii=False)

#     def export_links(self, user: LinktreeUser):
#         user_dir = Path(self.output_dir) / user.username
#         user_dir.mkdir(parents=True, exist_ok=True)

#         links = [{"url": link.url} for link in user.links]

#         with open(user_dir / "links.json", "w", encoding="utf-8") as f:
#             json.dump(links, f, indent=2, ensure_ascii=False)
