import asyncio
import json
from services.imgbb_service import ImgBBService
import logging
import os

logger = logging.getLogger(__name__)

async def upload_profile_image(user_info):
    """Uploads user profile image to ImgBB service and saves URL to JSON file
    
    Args:
        user_info: User profile object containing avatar image and username
        
    Returns:
        None: Updates user_info.image_url if upload is successful and creates JSON file
    """
    if hasattr(user_info, 'avartar_image') and user_info.avartar_image:
        print("\n[INFO] Uploading profile image to ImgBB...")
        logger.info("Starting profile image upload process")
        
        imgbb = ImgBBService()
        new_image_url = await imgbb.upload_image(user_info.avartar_image)
        
        if new_image_url:
            user_info.image_url = new_image_url
            print(f"[SUCCESS] Profile image uploaded to: {new_image_url}")
            logger.info(f"Successfully uploaded image to: {new_image_url}")
            
            # Save URL to JSON file
            try:
                if hasattr(user_info, 'username'):
                    # Create user directory if it doesn't exist
                    output_dir = "output"
                    user_dir = os.path.join(output_dir, user_info.username)
                    os.makedirs(user_dir, exist_ok=True)
                    
                    filename = os.path.join(user_dir, f"avatar_{user_info.username}.json")
                    avatar_data = {
                        "username": user_info.username,
                        "avatar_url": new_image_url,
                        "timestamp": str(asyncio.get_event_loop().time())
                    }
                    
                    with open(filename, 'w') as f:
                        json.dump(avatar_data, f, indent=4)
                    
                    print(f"[INFO] Saved avatar URL to {filename}")
                    logger.info(f"Successfully saved avatar data to {filename}")
                else:
                    print("[WARNING] User info missing username, cannot save JSON file")
                    logger.warning("User info missing username, skipping JSON file creation")
            except Exception as e:
                print(f"[ERROR] Failed to save avatar JSON file: {str(e)}")
                logger.error(f"Error saving avatar JSON file: {str(e)}")
        else:
            print("[WARNING] Failed to upload profile image, using original URL")
            logger.warning("Failed to upload profile image, using original URL")
