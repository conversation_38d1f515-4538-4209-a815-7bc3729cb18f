# LinkTree Scraper API

Uma API FastAPI para realizar scraping de perfis públicos do Linktree.

## 🚀 Funcionalidades

- **Health Check**: Verificação de saúde da API
- **Métricas**: Monitoramento de performance e uso
- **Scraping de Usuários**: Extração de dados públicos de perfis Linktree

## 📋 Pré-requisitos

- Python 3.8+
- Todas as dependências listadas em `requirements.txt`

## 🛠️ Instalação

1. **Instalar dependências**:

```bash
pip install -r requirements.txt
```

2. **Configurar variáveis de ambiente** (opcional):

```bash
cp .env.example .env
# Edite o arquivo .env conforme necessário
```

## 🚀 Executando a API

### Usando o script de inicialização:

```bash
python run_api.py
```

### Usando uvicorn diretamente:

```bash
uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
```

A API estará disponível em:

- **Servidor**: http://localhost:8000
- **Documentação**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 📚 Endpoints

### Health Check

- **GET** `/api/v1/health`
- Retorna o status de saúde da API

### Métricas

- **GET** `/api/v1/metrics`
- Retorna métricas de uso da aplicação

### Scraping de Usuário

- **POST** `/api/v1/scrape/user`
- Realiza scraping de um perfil Linktree

**Exemplo de Request**:

```json
{
  "user": "exemplo_usuario", // ou "https://linktr.ee/exemplo_usuario" ou "linktr.ee/exemplo_usuario"
  "info": "Informações adicionais sobre o negócio para melhor processamento", // opcional
  "template_name": "general" // opcional: general, tattoo, barber, transform (padrão: general)
}
```

**Exemplo de Response**:

```json
{
  "success": true,
  "message": "Scraping realizado com sucesso",
  "data": {
    "username": "exemplo_usuario",
    "url": "https://linktr.ee/exemplo_usuario",
    "avatar_image": "https://...",
    "id": 12345,
    "tier": "free",
    "isActive": true,
    "description": "Descrição do perfil",
    "number_of_links": 5,
    "links": [
      {
        "url": "https://example.com",
        "button_text": "Meu Site"
      }
    ]
  },
  "timestamp": "2025-01-07T18:10:00Z"
}
```

## 🎨 Templates de Processamento

A API suporta diferentes templates para otimização dos dados com IA, cada um especializado para diferentes tipos de negócio:

### Templates Disponíveis

| Template    | Descrição                                                                       |
| ----------- | ------------------------------------------------------------------------------- |
| `general`   | **Padrão** - Template genérico para qualquer tipo de negócio ou perfil          |
| `tattoo`    | Especializado para estúdios de tatuagem, tatuadores e profissionais de body art |
| `barber`    | Especializado para barbearias, barbeiros e salões de beleza masculinos          |
| `transform` | Template original/legado para transformação básica de dados                     |

### Como Usar

Adicione o parâmetro `template_name` na sua requisição:

```json
{
  "user": "barbeariatarantino",
  "template_name": "barber"
}
```

Se não especificado, será usado o template `general` por padrão.

## ⚙️ Configuração

A API pode ser configurada através de variáveis de ambiente com prefixo `LINKTREE_`:

| Variável                       | Padrão    | Descrição                        |
| ------------------------------ | --------- | -------------------------------- |
| `LINKTREE_HOST`                | `0.0.0.0` | Host do servidor                 |
| `LINKTREE_PORT`                | `8000`    | Porta do servidor                |
| `LINKTREE_DEBUG`               | `false`   | Modo debug                       |
| `LINKTREE_SCRAPER_TIMEOUT`     | `30`      | Timeout para scraping (segundos) |
| `LINKTREE_RATE_LIMIT_REQUESTS` | `100`     | Limite de requests por minuto    |

## 📝 Logs

A aplicação gera logs detalhados sobre:

- Requisições recebidas
- Operações de scraping
- Erros e exceções
- Métricas de performance

## 🔧 Desenvolvimento

### Estrutura do Projeto

```
api/
├── __init__.py
├── main.py              # Aplicação FastAPI principal
├── core/
│   ├── __init__.py
│   ├── config.py        # Configurações
│   └── metrics.py       # Sistema de métricas
├── routers/
│   ├── __init__.py
│   ├── health.py        # Endpoints de health/metrics
│   └── scraper.py       # Endpoints de scraping
├── schemas/
│   ├── __init__.py
│   ├── requests.py      # Schemas de request
│   └── responses.py     # Schemas de response
└── services/
    ├── __init__.py
    └── scraper_service.py # Lógica de scraping
```

### Adicionando novos endpoints

1. Crie um novo router em `api/routers/`
2. Defina os schemas em `api/schemas/`
3. Implemente a lógica em `api/services/`
4. Registre o router em `api/main.py`

## 🐛 Troubleshooting

### Problemas comuns:

1. **Erro de importação**: Verifique se todas as dependências estão instaladas
2. **Timeout durante scraping**: Ajuste `LINKTREE_SCRAPER_TIMEOUT`
3. **Rate limiting**: Verifique se não está excedendo os limites

### Debug mode:

Defina `LINKTREE_DEBUG=true` para ativar logs detalhados e reload automático.

## 📄 Licença

Este projeto é para uso interno da Avença Digital.
