import json
import logging
from tenacity import retry, stop_after_attempt, wait_exponential
import aiohttp
from aiohttp import ClientTimeout

logger = logging.getLogger(__name__)


class WebhookService:
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url
        self.timeout = ClientTimeout(total=30)
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.sent_hashes = set()  # Cache para evitar envios duplicados

    @retry(
        stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def send_to_webhook(self, json_data: dict, force_send: bool = False):
        """Send unified JSON data to webhook

        Args:
            json_data: Dados JSON a serem enviados
            force_send: Ignora verificação de duplicados e força o envio

        Returns:
            bool: True se os dados foram enviados, False se já foram enviados anteriormente
        """
        import hashlib

        # Gera hash dos dados para verificar duplicação
        data_hash = hashlib.md5(
            json.dumps(json_data, sort_keys=True).encode("utf-8")
        ).hexdigest()

        # Verifica se já foi enviado
        if not force_send and data_hash in self.sent_hashes:
            logger.info(f"Dados já enviados anteriormente (hash: {data_hash})")
            return False

        try:
            async with aiohttp.ClientSession(
                headers=self.headers, timeout=self.timeout
            ) as session:
                resp = await session.request(
                    method="POST", url=self.webhook_url, json=json_data
                )
                resp.raise_for_status()

                # Adiciona hash ao cache de envios
                self.sent_hashes.add(data_hash)
                logger.info(f"Successfully sent data to webhook: {self.webhook_url}")
                return True

        except aiohttp.ClientError as e:
            logger.error(f"Network error while sending to webhook: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error while sending to webhook: {str(e)}")
            raise
