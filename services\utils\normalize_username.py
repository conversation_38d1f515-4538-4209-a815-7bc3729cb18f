# Normalize username for Firebase

import re


def normalize_username(username: str) -> str:
    """
    Normalize username by removing special characters and converting to lowercase.

    Args:
        username (str): The username to normalize.

    Returns:
        str: The normalized username.
    """
    if not username:
        raise ValueError("Username cannot be empty")

    # Remove all non-alphanumeric characters and convert to lowercase
    normalized_username = re.sub(r"[^a-zA-Z0-9]", "", username).lower()

    if not normalized_username:
        raise ValueError("Normalized username cannot be empty")

    return normalized_username
