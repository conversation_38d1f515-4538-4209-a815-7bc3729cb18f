TRANSFORM_PROMPT_TEMPLATE = """

Sua tarefa é analisar e otimizar os dados fornecidos para criar uma página de links eficaz e organizada.IMPORTANTE: NUNCA invente ou adicione dados que não existam no JSON original. Apenas organize, otimize e melhore os dados existentes.

**Regras e Diretrizes gerais:**
0. NUNCA invente ou adicione dados que não existam nos dados fornecidos. NUNCA.
1. Todo o conteúdo deve estar em português do Brasil.
2. Use uma hierarquia clara para facilitar a navegação.
3. Certifique-se de que os links estejam válidos e completos, incluindo `http` ou `https`.
4. Destaque redes sociais para maior visibilidade.
5. Otimize descrições para maior entendimento e aplicadas ao contexto "página de links que será visualizada por clientes/alunos/pessoas interessadas
6. Garan<PERSON> que os links de redes sociais estejam presentes
7. Caso não tenha um link próprio da localização, e quando houver endereço/localização, gere um link "https://www.google.com/maps/dir/?api=1&destination="formattedAddress", sendo o formattedAddress "street,number,neighborhood,city,state". Inclua tanto como "links" como "socialMedia". Defina um texto intuitivo para o botão, como "Conheça nossa localização" ou similar.
8. Quando houver número de telefone, crie um link "tel:XXXXX", e inclua tanto como botão como como "socialMedia". Defina um texto intuitivo para o botão. Defina também como "socialMedia" o WhatsApp, YouTube e Maps/Localização, porém mantenha como links quando considerar que isso seja adequado. Caso não tenha um número explícito nos dados fornecidos, tente extrair o número da URL do WhatsApp e formate-o para o padrão, sendo os dois primeiros dígitos o DDD e o restante o número de telefone, não inclua o código do país.
8.1 No "socialMedia", evite repetir a mesma rede social mais de uma vez, mantenha apenas um para cada.
9. Incluir TODAS as seções requeridas (gallery, genericSection, featuresSection, servicesSection, links, reviews, settings, socialMedias, user, video, location, team)
10. Marque como "false" seções que não tenham dados no JSON original ou que não serão utilizadas no contexto.

**Sobre `gallery`:**
1. Se não houver url de imagens no JSON original, defina showGallery como true e defina dados genéricos e as urls para cada contexto, como:
1.1 Para studio de pilates, fisioterapia, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/01/20/pilates.jpg,https://db.avenca.cloud/images/2025/01/20/pilates2.jpg, https://db.avenca.cloud/images/2025/01/20/pilates3.jpg, https://db.avenca.cloud/images/2025/01/20/pilates4.jpg, https://db.avenca.cloud/images/2025/01/20/pilates5.jpg.
1.2 Para box de crossfit, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/01/20/crossfit.jpg, https://db.avenca.cloud/images/2025/01/20/crossfit2.jpg, https://db.avenca.cloud/images/2025/01/20/crossfit3.jpg, https://db.avenca.cloud/images/2025/01/20/crossfit4.jpg, https://db.avenca.cloud/images/2025/01/20/crossfit5.jpg.
1.3 Para academia, academia de musculação, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/01/20/academia.jpg, https://db.avenca.cloud/images/2025/01/20/academia2.jpg, https://db.avenca.cloud/images/2025/01/20/academia3.jpg, https://db.avenca.cloud/images/2025/01/20/academia4.jpg, https://db.avenca.cloud/images/2025/01/20/academia5.jpg.
1.4 Para centro de estética, salão de beleza, e/ou similar, utilize as imagens: https://images.unsplash.com/photo-1660505102581-85cffa4e6550?q=80&w=1888&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D e https://images.unsplash.com/photo-1620331307581-1e7d27da7ab6?q=80&w=2011&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
1.5 Para imobiliárias, corretores de imóveis, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/01/27/imob5.jpg, https://db.avenca.cloud/images/2025/01/27/imob.jpg,https://db.avenca.cloud/images/2025/01/27/imob2.jpg, https://db.avenca.cloud/images/2025/01/27/imob3.jpg, https://db.avenca.cloud/images/2025/01/27/imob4.jpg,
1.7 Para dentistas, clínicas odontológicas, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/01/27/dental.jpg, https://db.avenca.cloud/images/2025/01/27/dental2.jpg, https://db.avenca.cloud/images/2025/01/27/dental3.jpg, https://db.avenca.cloud/images/2025/01/27/dental4.jpg, https://db.avenca.cloud/images/2025/01/27/dental5.jpg,

1.8 Para tatuadores, estúdios de tatuagem, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/07/01/tattoo1.jpg, https://db.avenca.cloud/images/2025/07/01/tattoo2.jpg, https://db.avenca.cloud/images/2025/07/01/tattoo3.jpg, https://db.avenca.cloud/images/2025/07/01/tattoo4.jpg, https://db.avenca.cloud/images/2025/07/01/tattoo5.jpg,

**Sobre `genericSection`, `featuresSection` e "servicesSection":**
0. Seção com Carousel de cards interativos em sliders horizontais.
1. Sempre que possível, gere os 3 componentes, `genericSection`, `featuresSection` e "servicesSection", com no mínimo 4 itens em cada carousel, sempre buscando o máximo possível dentro do contexto.
1.2 Usada para destacar:
Produtos, serviços, ferramentas, equipes, unidades, professores, categorias, especializações, diferenciais, etc.
1.3 Pode incluir nenhum ou 2 botões por item, dependendo do contexto. Quando o contexto for um por exemplo um **serviço**, utilize os botões "Agendar" e "Saiba mais". Quando o contexto não necessitar de botões, defina o valor de "#" tanto para o "icon" quanto para o "ulr" no primaryButton e secondaryButton.
1.4 Adapte os textos dos botões ao contexto.
1.5 Se não houver imagens nos dados fornecidos, utilize as URL's do "gallery".

**Sobre `video`:**
1. Se não houver vídeos do YouTube no JSON original, defina showVideo como false e deixe url vazia

**Sobre `reviews`:**
1. Se não houver avaliações no JSON original, defina showReviews como true e invente 5 possíveis reviews de 5 estrelas relacionados ao contexto do negócio.
2. Mantenha todos os campos obrigatórios.

**Sobre `location`:**
1. Se houver informações de localização no JSON original, mantenha todos os dados existentes.
2. Se não houver informações de localização, defina enabled como true e crie dados mockados apropriados para o contexto:
   - address: Crie um endereço fictício mas realista para a cidade/região do negócio
   - contact: Use o telefone extraído do WhatsApp ou crie um número fictício no formato brasileiro
   - googleMapsUrl: Gere uma URL do Google Maps com coordenadas fictícias da região
   - hours: Crie horários de funcionamento típicos para o tipo de negócio
3. Quando enabled for true, garanta que todos os campos obrigatórios estejam preenchidos:
   - address: city, country, state, street, zipCode
   - contact: phone, whatsapp
   - googleMapsUrl: URL válida do Google Maps
   - hours: closed, weekdays, weekends
4. Use as informações de localização para gerar links de mapa e telefone nas seções de links e socialMedia.

**Sobre `team`:**
1. Se houver informações de equipe no JSON original, mantenha todos os dados existentes.
2. Se não houver informações de equipe, defina enabled como true e crie dados mockados apropriados para o contexto:
   - title: "Nossa Equipe", "Nosso Time" ou similar
   - description: Descrição da seção seguindo o mesmo padrão das outras seções (ex: "Conheça nossa equipe de profissionais qualificados")
   - members: Crie 2-3 membros fictícios com nomes brasileiros realistas, cargos apropriados para o tipo de negócio
   - photo: Use a mesma foto do avatar do usuário ou URLs genéricas de placeholder
   - url: Links para redes sociais fictícias ou deixe vazio se apropriado
3. Quando enabled for true, garanta que todos os campos obrigatórios estejam preenchidos:
   - title: Título da seção (ex: "Nossa Equipe", "Nosso Time", "Sobre nós")
   - description: Descrição da seção seguindo o mesmo padrão das outras seções
   - members: Lista de membros com name, role, photo, url
4. Para cada membro da equipe, inclua informações completas: nome, cargo/função, foto e link para perfil. Se não houver link para perfil, defina o valor como "".
5. Garanta que todos os membros da equipe tenham informações completas: nome, cargo/função, foto e link para perfil.
5.1 Link pode ser opcional. Se não houver link para perfil, defina o valor como "".
6. Use as informações da equipe para criar links personalizados nas seções de links e socialMedia quando apropriado.

**Sobre `user.brandLogo`:**
1. Se houver informações de logo da marca no JSON original, mantenha os dados existentes.
2. Se não houver informações de brandLogo, defina o valor como o mesmo do campo `avatar`.
3. O brandLogo deve sempre ter um valor válido (URL de imagem).

**Sobre `settings.ogImage` e o `user.heroImage`:**
1. Se houver informações de imagem Open Graph no JSON original, mantenha os dados existentes.
2. Se não houver informações de ogImage, defina o valor como o mesmo do campo `favicon`.
3. O ogImage e heroImage deve sempre ter um valor válido (URL de imagem).

**Exemplos de dados mockados por contexto:**
- **Estúdios de tatuagem**: Location em São Paulo/SP, team com "Tatuador Principal", "Artista Especialista", "Recepcionista"
- **Academias/CrossFit**: Location em cidade apropriada, team com "Personal Trainer", "Instrutor", "Nutricionista"
- **Clínicas/Consultórios**: Location em centro médico, team com "Dr./Dra. [Nome]", "Enfermeiro(a)", "Recepcionista"
- **Salões de beleza**: Location em centro comercial, team com "Cabeleireiro(a)", "Manicure", "Esteticista"
- **Restaurantes**: Location em área comercial, team com "Chef", "Garçom/Garçonete", "Gerente"

**Visual, design e usabilidade:**
1. Prefira tons claros para o background (próximos ao branco).
2. Garanta contraste adequado entre texto, botões e background.
3. Use cores fortes e contrastantes entre a cor dos botões (primary) e cor dos links dos botões (linkText).
4. Para itens sem imagens, use uma string vazia ("") no campo image
5. Use ícones apropriados do Font Awesome (fa fa-*) para cada tipo de link:
   - WhatsApp: fa fa-whatsapp
   - Instagram: fa fa-instagram
   - Facebook: fa fa-facebook
   - YouTube: fa fa-youtube
   - Email: fa fa-envelope
   - Localização: fa fa-map
   - Site/Blog: fa fa-globe
   - Telefone: fa fa-phone
   - Endereço: fa fa-map
   - Agendar: fa fa-calendar
   - Ver mais: fa fa-eye
   - Android: fa fa-googleplay
   - iOS: fa fa-apple

Input JSON:
{input_json}

Transforme estes dados em um formato otimizado para uma página de links mantendo a seguinte estrutura:
{format_instructions}

Lembre-se de:
- NUNCA inventar dados que não existam no JSON original
- Leve SEMPRE em consideração o contraste ao definir as cores dos botões, textos e background, garantindo a melhor experiència e usabilidade possível. Tome cuidado especial com cores claras como "amarelo".

**Informações adicionais para contexto:**
{additional_info}

**Instruções personalizadas:**

"""

TATTOO_PROMPT_TEMPLATE = """

Sua tarefa é analisar e otimizar os dados fornecidos para criar uma página de links eficaz e organizada voltada para **estúdios de tatuagem, tatuadores ou profissionais da área de body art**.

> IMPORTANTE: **NUNCA invente ou adicione dados que não existam no JSON original. Apenas organize, otimize e melhore os dados existentes.**

**Regras e Diretrizes gerais:**
0. NUNCA invente ou adicione dados que não existam nos dados fornecidos. NUNCA.
1. Todo o conteúdo deve estar em português do Brasil.
2. Use uma hierarquia clara para facilitar a navegação.
3. Certifique-se de que os links estejam válidos e completos, incluindo `http` ou `https`.
4. Destaque redes sociais para maior visibilidade.
5. Otimize descrições para maior entendimento e aplicadas ao contexto "página de links que será visualizada por clientes/pessoas interessadas
6. Garanta que os links de redes sociais estejam presentes
7. Caso não tenha um link próprio da localização, e quando houver endereço/localização, gere um link "https://www.google.com/maps/dir/?api=1&destination="formattedAddress", sendo o formattedAddress "street,number,neighborhood,city,state". Inclua tanto como "links" como "socialMedia". Defina um texto intuitivo para o botão, como "Conheça nossa localização" ou similar.
8. Quando houver número de telefone, crie um link "tel:XXXXX", e inclua tanto como botão como como "socialMedia". Defina um texto intuitivo para o botão. Defina também como "socialMedia" o WhatsApp, YouTube e Maps/Localização, porém mantenha como links quando considerar que isso seja adequado. Caso não tenha um número explícito nos dados fornecidos, tente extrair o número da URL do WhatsApp e formate-o para o padrão, sendo os dois primeiros dígitos o DDD e o restante o número de telefone, não inclua o código do país.
8.1 No "socialMedia", evite repetir a mesma rede social mais de uma vez, mantenha apenas um para cada.
9. Incluir TODAS as seções requeridas (gallery, genericSection, featuresSection, servicesSection, links, reviews, settings, socialMedias, user, video, location, team)
10. Marque como "false" seções que não tenham dados no JSON original ou que não serão utilizadas no contexto.

**Sobre `gallery`:**
1. Se não houver url de imagens no JSON original, defina showGallery como true e defina dados genéricos e as urls para cada contexto, como:
https://db.avenca.cloud/images/2025/07/01/tattoo1.jpg
https://db.avenca.cloud/images/2025/07/01/tattoo2.jpg
https://db.avenca.cloud/images/2025/07/01/tattoo3.jpg
https://db.avenca.cloud/images/2025/07/01/tattoo4.jpg
https://db.avenca.cloud/images/2025/07/01/tattoo5.jpg

**Sobre `genericSection`, `featuresSection` e "servicesSection":**
0. Seção com Carousel de cards interativos em sliders horizontais.
1. Sempre que possível, gere os 3 componentes, `genericSection`, `featuresSection` e "servicesSection", com no mínimo 4 itens em cada carousel, sempre buscando o máximo possível dentro do contexto.
1.2 Usada para destacar:
Estilos de tatuagem** (realismo, old school, fine line etc), Portfólio de artistas, Diferenciais do estúdio** (higiene, equipamentos, ambiente, certificações), Serviços complementares** (piercing, retoques, consultoria de arte).
1.3 Pode incluir nenhum ou 2 botões por item, dependendo do contexto. Quando o contexto for um por exemplo um **serviço**, utilize os botões "Agendar" e "Saiba mais". Quando o contexto não necessitar de botões, defina o valor de "#" tanto para o "icon" quanto para o "ulr" no primaryButton e secondaryButton.
1.4 Adapte os textos dos botões ao contexto.
1.5 Se não houver imagens nos dados fornecidos, utilize as URL's do "gallery".

**Sobre `video`:**
1. Se não houver vídeos do YouTube no JSON original, defina showVideo como false e deixe url vazia

**Sobre `reviews`:**
1. Se não houver avaliações no JSON original, defina showReviews como true e invente 5 possíveis reviews de 5 estrelas relacionados ao contexto do negócio.
2. Mantenha todos os campos obrigatórios.

**Sobre `location`:**
1. Se houver informações de localização no JSON original, mantenha todos os dados existentes.
2. Se não houver informações de localização, defina enabled como true e crie dados mockados apropriados para o contexto:
   - address: Crie um endereço fictício mas realista para a cidade/região do negócio
   - contact: Use o telefone extraído do WhatsApp ou crie um número fictício no formato brasileiro
   - googleMapsUrl: Gere uma URL do Google Maps com coordenadas fictícias da região
   - hours: Crie horários de funcionamento típicos para o tipo de negócio
3. Quando enabled for true, garanta que todos os campos obrigatórios estejam preenchidos:
   - address: city, country, state, street, zipCode
   - contact: phone, whatsapp
   - googleMapsUrl: URL válida do Google Maps
   - hours: closed, weekdays, weekends
4. Use as informações de localização para gerar links de mapa e telefone nas seções de links e socialMedia.

**Sobre `team`:**
1. Se houver informações de equipe no JSON original, mantenha todos os dados existentes.
2. Se não houver informações de equipe, defina enabled como true e crie dados mockados apropriados para o contexto:
   - title: "Nossa Equipe", "Nosso Time" ou similar
   - description: Descrição da seção seguindo o mesmo padrão das outras seções (ex: "Conheça nossa equipe de profissionais qualificados")
   - members: Crie 2-3 membros fictícios com nomes brasileiros realistas, cargos apropriados para o tipo de negócio
   - photo: Use a mesma foto do avatar do usuário ou URLs genéricas de placeholder
   - url: Links para redes sociais fictícias ou deixe vazio se apropriado
3. Quando enabled for true, garanta que todos os campos obrigatórios estejam preenchidos:
   - title: Título da seção (ex: "Nossa Equipe", "Nosso Time")
   - description: Descrição da seção seguindo o mesmo padrão das outras seções
   - members: Lista de membros com name, role, photo, url
4. Para cada membro da equipe, inclua informações completas: nome, cargo/função, foto e link para perfil.
5. Garanta que todos os membros da equipe tenham informações completas: nome, cargo/função, foto e link para perfil.
5.1 Link pode ser opcional. Se não houver link para perfil, defina o valor como "".
6. Use as informações da equipe para criar links personalizados nas seções de links e socialMedia quando apropriado.

**Sobre `user.brandLogo`:**
1. Se houver informações de logo da marca no JSON original, mantenha os dados existentes.
2. Se não houver informações de brandLogo, defina o valor como o mesmo do campo `avatar`.
3. O brandLogo deve sempre ter um valor válido (URL de imagem).

**Sobre `settings.ogImage` e o `user.heroImage`:**
1. Se houver informações de imagem Open Graph no JSON original, mantenha os dados existentes.
2. Se não houver informações de ogImage, defina o valor como o mesmo do campo `favicon`.
3. O ogImage e heroImage deve sempre ter um valor válido (URL de imagem).

**Exemplos de dados mockados para estúdios de tatuagem:**
- **Location**: Endereço em São Paulo/SP, Rio de Janeiro/RJ ou cidade apropriada, horários típicos (Ter-Sáb 10h-19h, Dom fechado)
- **Team**: "Tatuador Principal", "Artista Red Blackwork", "Piercer", "Recepcionista" com nomes brasileiros realistas
- **Cargos específicos**: "Especialista em Realismo", "Expert em Fine Line", "Tatuador Old School", "Body Piercer"

**Visual, design e usabilidade:**
1. Prefira tons claros para o background (próximos ao branco).
2. Garanta contraste adequado entre texto, botões e background.
3. Use cores fortes e contrastantes entre a cor dos botões (primary) e cor dos links dos botões (linkText).
4. Para itens sem imagens, use uma string vazia ("") no campo image
5. Use ícones apropriados do Font Awesome (fa fa-*) para cada tipo de link:
   - WhatsApp: fa fa-whatsapp
   - Instagram: fa fa-instagram
   - Facebook: fa fa-facebook
   - YouTube: fa fa-youtube
   - Email: fa fa-envelope
   - Localização: fa fa-map
   - Site/Blog: fa fa-globe
   - Telefone: fa fa-phone
   - Endereço: fa fa-map
   - Agendar: fa fa-calendar
   - Ver mais: fa fa-eye
   - Android: fa fa-googleplay
   - iOS: fa fa-apple

Input JSON:
{input_json}

Transforme estes dados em um formato otimizado para uma página de links mantendo a seguinte estrutura:
{format_instructions}

Lembre-se de:
- NUNCA inventar dados que não existam no JSON original
- Leve SEMPRE em consideração o contraste ao definir as cores dos botões, textos e background, garantindo a melhor experiència e usabilidade possível. Tome cuidado especial com cores claras como "amarelo".

**Informações adicionais para contexto:**
{additional_info}

**Instruções personalizadas:**

"""

BARBER_PROMPT_TEMPLATE = """

Sua tarefa é analisar e otimizar os dados fornecidos para criar uma página de links eficaz e organizada voltada para **barbearias, barbeiros, cabeleireiros ou profissionais da área de beleza masculina e cuidados capilares**.

> IMPORTANTE: **NUNCA invente ou adicione dados que não existam no JSON original. Apenas organize, otimize e melhore os dados existentes.**

**Regras e Diretrizes gerais:**
0. NUNCA invente ou adicione dados que não existam nos dados fornecidos. NUNCA.
1. Todo o conteúdo deve estar em português do Brasil.
2. Use uma hierarquia clara para facilitar a navegação.
3. Certifique-se de que os links estejam válidos e completos, incluindo `http` ou `https`.
4. Destaque redes sociais para maior visibilidade.
5. Otimize descrições para maior entendimento e aplicadas ao contexto "página de links que será visualizada por clientes/pessoas interessadas".
6. Garanta que os links de redes sociais estejam presentes.
7. Caso não tenha um link próprio da localização, e quando houver endereço/localização, gere um link "https://www.google.com/maps/dir/?api=1&destination="formattedAddress", sendo o formattedAddress "street,number,neighborhood,city,state". Inclua tanto como "links" como "socialMedia". Defina um texto intuitivo para o botão, como "Conheça nossa localização" ou similar.
8. Quando houver número de telefone, crie um link "tel:XXXXX", e inclua tanto como botão como como "socialMedia". Defina um texto intuitivo para o botão. Defina também como "socialMedia" o WhatsApp, YouTube e Maps/Localização, porém mantenha como links quando considerar que isso seja adequado. Caso não tenha um número explícito nos dados fornecidos, tente extrair o número da URL do WhatsApp e formate-o para o padrão, sendo os dois primeiros dígitos o DDD e o restante o número de telefone, não inclua o código do país.
8.1 No "socialMedia", evite repetir a mesma rede social mais de uma vez, mantenha apenas um para cada.
9. Incluir TODAS as seções requeridas (gallery, genericSection, featuresSection, servicesSection, links, reviews, settings, socialMedias, user, video, location, team)
10. Marque como "false" seções que não tenham dados no JSON original ou que não serão utilizadas no contexto.

**Sobre `gallery`:**
1. Se não houver url de imagens no JSON original, defina showGallery como true e defina dados genéricos e as urls para cada contexto, como:
https://db.avenca.cloud/images/2025/07/10/barbershop1.jpg
https://db.avenca.cloud/images/2025/07/10/barbershop2.jpg
https://db.avenca.cloud/images/2025/07/10/barbershop3.jpg
https://db.avenca.cloud/images/2025/07/10/barbershop4.jpg
https://db.avenca.cloud/images/2025/07/10/barbershop5.jpg

**Sobre `genericSection`, `featuresSection` e "servicesSection":**
0. Seção com Carousel de cards interativos em sliders horizontais.
1. Sempre que possível, gere os 3 componentes, `genericSection`, `featuresSection` e "servicesSection", com no mínimo 4 itens em cada carousel, sempre buscando o máximo possível dentro do contexto.
1.2 Usada para destacar:
   - **Estilos de corte** (fade, razor part, undercut, etc)
   - **Cuidados oferecidos** (hidratação, barba terapia, design de barba)
   - **Diferenciais do espaço** (ambiente climatizado, barbearia retrô, atendimento com hora marcada, produtos premium)
1.3 Pode incluir nenhum ou 2 botões por item, dependendo do contexto. Quando o contexto for um **serviço**, utilize os botões "Agendar" e "Saiba mais". Quando o contexto não necessitar de botões, defina o valor de "#" tanto para o "icon" quanto para o "url" no primaryButton e secondaryButton.
1.4 Adapte os textos dos botões ao contexto.
1.5 Se não houver imagens nos dados fornecidos, utilize as URL's do "gallery".

**Sobre `video`:**
1. Se não houver vídeos do YouTube no JSON original, defina showVideo como false e deixe url vazia

**Sobre `reviews`:**
1. Se não houver avaliações no JSON original, defina showReviews como true e invente 5 possíveis reviews de 5 estrelas relacionados ao contexto do negócio.
2. Mantenha todos os campos obrigatórios.

**Sobre `location`:**
1. Se houver informações de localização no JSON original, mantenha todos os dados existentes.
2. Se não houver informações de localização, defina enabled como true e crie dados mockados apropriados para o contexto:
   - address: Crie um endereço fictício mas realista para a cidade/região do negócio
   - contact: Use o telefone extraído do WhatsApp ou crie um número fictício no formato brasileiro
   - googleMapsUrl: Gere uma URL do Google Maps com coordenadas fictícias da região
   - hours: Crie horários de funcionamento típicos para o tipo de negócio
3. Quando enabled for true, garanta que todos os campos obrigatórios estejam preenchidos:
   - address: city, country, state, street, zipCode
   - contact: phone, whatsapp
   - googleMapsUrl: URL válida do Google Maps
   - hours: closed, weekdays, weekends
4. Use as informações de localização para gerar links de mapa e telefone nas seções de links e socialMedia.

**Sobre `team`:**
1. Se houver informações de equipe no JSON original, mantenha todos os dados existentes.
2. Se não houver informações de equipe, defina enabled como true e crie dados mockados apropriados para o contexto:
   - title: "Nossa Equipe", "Nosso Time" ou similar
   - description: Descrição da seção seguindo o mesmo padrão das outras seções (ex: "Conheça nossa equipe de profissionais qualificados")
   - members: Crie 2-3 membros fictícios com nomes brasileiros realistas, cargos apropriados para o tipo de negócio
   - photo: Use a mesma foto do avatar do usuário ou URLs genéricas de placeholder
   - url: Links para redes sociais fictícias ou deixe vazio se apropriado
3. Quando enabled for true, garanta que todos os campos obrigatórios estejam preenchidos:
   - title: Título da seção (ex: "Nossa Equipe", "Nosso Time")
   - description: Descrição da seção seguindo o mesmo padrão das outras seções
   - members: Lista de membros com name, role, photo, url
4. Para cada membro da equipe, inclua informações completas: nome, cargo/função, foto e link para perfil.
5. Garanta que todos os membros da equipe tenham informações completas: nome, cargo/função, foto e link para perfil.
5.1 Link pode ser opcional. Se não houver link para perfil, defina o valor como "".
6. Use as informações da equipe para criar links personalizados nas seções de links e socialMedia quando apropriado.

**Sobre `user.brandLogo`:**
1. Se houver informações de logo da marca no JSON original, mantenha os dados existentes.
2. Se não houver informações de brandLogo, defina o valor como o mesmo do campo `avatar`.
3. O brandLogo deve sempre ter um valor válido (URL de imagem).

**Sobre `settings.ogImage` e o `user.heroImage`:**
1. Se houver informações de imagem Open Graph no JSON original, mantenha os dados existentes.
2. Se não houver informações de ogImage, defina o valor como o mesmo do campo `favicon`.
3. O ogImage e heroImage deve sempre ter um valor válido (URL de imagem).

**Exemplos de dados mockados para barbearias:**
- **Location**: Endereço em São Paulo/SP, Rio de Janeiro/RJ ou cidade apropriada, horários típicos (Ter-Sáb 10h-20h, Dom fechado)
- **Team**: "Barbeiro Sênior", "Especialista em Barba", "Estilista Capilar", "Atendimento e Recepção" com nomes brasileiros realistas
- **Cargos específicos**: "Especialista em Fade", "Expert em Navalhado", "Barbeiro Clássico", "Técnico em Hidratação Capilar"

**Visual, design e usabilidade:**
1. Prefira tons claros para o background (próximos ao branco).
2. Garanta contraste adequado entre texto, botões e background.
3. Use cores fortes e contrastantes entre a cor dos botões (primary) e cor dos links dos botões (linkText).
4. Para itens sem imagens, use uma string vazia ("") no campo image
5. Use ícones apropriados do Font Awesome (fa fa-*) para cada tipo de link:
   - WhatsApp: fa fa-whatsapp
   - Instagram: fa fa-instagram
   - Facebook: fa fa-facebook
   - YouTube: fa fa-youtube
   - Email: fa fa-envelope
   - Localização: fa fa-map
   - Site/Blog: fa fa-globe
   - Telefone: fa fa-phone
    - Endereço: fa fa-map
   - Agendar: fa fa-calendar
   - Ver mais: fa fa-eye
   - Android: fa fa-googleplay
   - iOS: fa fa-apple

Input JSON:
{input_json}

Transforme estes dados em um formato otimizado para uma página de links mantendo a seguinte estrutura:
{format_instructions}

Lembre-se de:
- NUNCA inventar dados que não existam no JSON original
- Leve SEMPRE em consideração o contraste ao definir as cores dos botões, textos e background, garantindo a melhor experiência e usabilidade possível. Tome cuidado especial com cores claras como "amarelo".

**Informações adicionais para contexto:**
{additional_info}

**Instruções personalizadas:**

"""

GENERAL_PROMPT_TEMPLATE = """

Sua tarefa é analisar e otimizar os dados fornecidos para criar uma página de links eficaz e organizada.IMPORTANTE: NUNCA invente ou adicione dados que não existam no JSON original. Apenas organize, otimize e melhore os dados existentes.

**Regras e Diretrizes gerais:**
0. NUNCA invente ou adicione dados que não existam nos dados fornecidos. NUNCA.
1. Todo o conteúdo deve estar em português do Brasil.
2. Use uma hierarquia clara para facilitar a navegação.
3. Certifique-se de que os links estejam válidos e completos, incluindo `http` ou `https`.
4. Destaque redes sociais para maior visibilidade.
5. Otimize descrições para maior entendimento e aplicadas ao contexto "página de links que será visualizada por clientes/alunos/pessoas interessadas
6. Garanta que os links de redes sociais estejam presentes
7. Caso não tenha um link próprio da localização, e quando houver endereço/localização, gere um link "https://www.google.com/maps/dir/?api=1&destination="formattedAddress", sendo o formattedAddress "street,number,neighborhood,city,state". Inclua tanto como "links" como "socialMedia". Defina um texto intuitivo para o botão, como "Conheça nossa localização" ou similar.
8. Quando houver número de telefone, crie um link "tel:XXXXX", e inclua tanto como botão como como "socialMedia". Defina um texto intuitivo para o botão. Defina também como "socialMedia" o WhatsApp, YouTube e Maps/Localização, porém mantenha como links quando considerar que isso seja adequado. Caso não tenha um número explícito nos dados fornecidos, tente extrair o número da URL do WhatsApp e formate-o para o padrão, sendo os dois primeiros dígitos o DDD e o restante o número de telefone, não inclua o código do país.
8.1 No "socialMedia", evite repetir a mesma rede social mais de uma vez, mantenha apenas um para cada.
9. Incluir TODAS as seções requeridas (gallery, genericSection, featuresSection, servicesSection, links, reviews, settings, socialMedias, user, video, location, team)
10. Marque como "false" seções que não tenham dados no JSON original ou que não serão utilizadas no contexto.

**Sobre `gallery`:**
1. Se não houver url de imagens no JSON original, defina showGallery como true e defina dados genéricos e as urls para cada contexto, como:
1.1 Para studio de pilates, fisioterapia, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/01/20/pilates.jpg,https://db.avenca.cloud/images/2025/01/20/pilates2.jpg, https://db.avenca.cloud/images/2025/01/20/pilates3.jpg, https://db.avenca.cloud/images/2025/01/20/pilates4.jpg, https://db.avenca.cloud/images/2025/01/20/pilates5.jpg.
1.2 Para box de crossfit, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/01/20/crossfit.jpg, https://db.avenca.cloud/images/2025/01/20/crossfit2.jpg, https://db.avenca.cloud/images/2025/01/20/crossfit3.jpg, https://db.avenca.cloud/images/2025/01/20/crossfit4.jpg, https://db.avenca.cloud/images/2025/01/20/crossfit5.jpg.
1.3 Para academia, academia de musculação, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/01/20/academia.jpg, https://db.avenca.cloud/images/2025/01/20/academia2.jpg, https://db.avenca.cloud/images/2025/01/20/academia3.jpg, https://db.avenca.cloud/images/2025/01/20/academia4.jpg, https://db.avenca.cloud/images/2025/01/20/academia5.jpg.
1.4 Para centro de estética, salão de beleza, e/ou similar, utilize as imagens: https://images.unsplash.com/photo-1660505102581-85cffa4e6550?q=80&w=1888&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D e https://images.unsplash.com/photo-1620331307581-1e7d27da7ab6?q=80&w=2011&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
1.5 Para imobiliárias, corretores de imóveis, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/01/27/imob5.jpg, https://db.avenca.cloud/images/2025/01/27/imob.jpg,https://db.avenca.cloud/images/2025/01/27/imob2.jpg, https://db.avenca.cloud/images/2025/01/27/imob3.jpg, https://db.avenca.cloud/images/2025/01/27/imob4.jpg,
1.7 Para dentistas, clínicas odontológicas, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/01/27/dental.jpg, https://db.avenca.cloud/images/2025/01/27/dental2.jpg, https://db.avenca.cloud/images/2025/01/27/dental3.jpg, https://db.avenca.cloud/images/2025/01/27/dental4.jpg, https://db.avenca.cloud/images/2025/01/27/dental5.jpg,

1.8 Para tatuadores, estúdios de tatuagem, e/ou similar, utilize as imagens: https://db.avenca.cloud/images/2025/07/01/tattoo1.jpg, https://db.avenca.cloud/images/2025/07/01/tattoo2.jpg, https://db.avenca.cloud/images/2025/07/01/tattoo3.jpg, https://db.avenca.cloud/images/2025/07/01/tattoo4.jpg, https://db.avenca.cloud/images/2025/07/01/tattoo5.jpg,

**Sobre `genericSection`, `featuresSection` e "servicesSection":**
0. Seção com Carousel de cards interativos em sliders horizontais.
1. Sempre que possível, gere os 3 componentes, `genericSection`, `featuresSection` e "servicesSection", com no mínimo 4 itens em cada carousel, sempre buscando o máximo possível dentro do contexto.
1.2 Usada para destacar:
Produtos, serviços, ferramentas, equipes, unidades, professores, categorias, especializações, diferenciais, etc.
1.3 Pode incluir nenhum ou 2 botões por item, dependendo do contexto. Quando o contexto for um por exemplo um **serviço**, utilize os botões "Agendar" e "Saiba mais". Quando o contexto não necessitar de botões, defina o valor de "#" tanto para o "icon" quanto para o "ulr" no primaryButton e secondaryButton.
1.4 Adapte os textos dos botões ao contexto.
1.5 Se não houver imagens nos dados fornecidos, utilize as URL's do "gallery".

**Sobre `video`:**
1. Se não houver vídeos do YouTube no JSON original, defina showVideo como false e deixe url vazia

**Sobre `reviews`:**
1. Se não houver avaliações no JSON original, defina showReviews como true e invente 5 possíveis reviews de 5 estrelas relacionados ao contexto do negócio.
2. Mantenha todos os campos obrigatórios.

**Sobre `location`:**
1. Se houver informações de localização no JSON original, mantenha todos os dados existentes.
2. Se não houver informações de localização, defina enabled como true e crie dados mockados apropriados para o contexto:
   - address: Crie um endereço fictício mas realista para a cidade/região do negócio
   - contact: Use o telefone extraído do WhatsApp ou crie um número fictício no formato brasileiro
   - googleMapsUrl: Gere uma URL do Google Maps com coordenadas fictícias da região
   - hours: Crie horários de funcionamento típicos para o tipo de negócio
3. Quando enabled for true, garanta que todos os campos obrigatórios estejam preenchidos:
   - address: city, country, state, street, zipCode
   - contact: phone, whatsapp
   - googleMapsUrl: URL válida do Google Maps
   - hours: closed, weekdays, weekends
4. Use as informações de localização para gerar links de mapa e telefone nas seções de links e socialMedia.

**Sobre `team`:**
1. Se houver informações de equipe no JSON original, mantenha todos os dados existentes.
2. Se não houver informações de equipe, defina enabled como true e crie dados mockados apropriados para o contexto:
   - title: "Nossa Equipe", "Nosso Time" ou similar
   - description: Descrição da seção seguindo o mesmo padrão das outras seções (ex: "Conheça nossa equipe de profissionais qualificados")
   - members: Crie 2-3 membros fictícios com nomes brasileiros realistas, cargos apropriados para o tipo de negócio
   - photo: Use a mesma foto do avatar do usuário ou URLs genéricas de placeholder
   - url: Links para redes sociais fictícias ou deixe vazio se apropriado
3. Quando enabled for true, garanta que todos os campos obrigatórios estejam preenchidos:
   - title: Título da seção (ex: "Nossa Equipe", "Nosso Time", "Sobre nós")
   - description: Descrição da seção seguindo o mesmo padrão das outras seções
   - members: Lista de membros com name, role, photo, url
4. Para cada membro da equipe, inclua informações completas: nome, cargo/função, foto e link para perfil.
5. Garanta que todos os membros da equipe tenham informações completas: nome, cargo/função, foto e link para perfil.
5.1 Link pode ser opcional. Se não houver link para perfil, defina o valor como "".
6. Use as informações da equipe para criar links personalizados nas seções de links e socialMedia quando apropriado.

**Sobre `user.brandLogo`:**
1. Se houver informações de logo da marca no JSON original, mantenha os dados existentes.
2. Se não houver informações de brandLogo, defina o valor como o mesmo do campo `avatar`.
3. O brandLogo deve sempre ter um valor válido (URL de imagem).

**Sobre `settings.ogImage` e o `user.heroImage`:**
1. Se houver informações de imagem Open Graph no JSON original, mantenha os dados existentes.
2. Se não houver informações de ogImage, defina o valor como o mesmo do campo `favicon`.
3. O ogImage e heroImage deve sempre ter um valor válido (URL de imagem).

**Exemplos de dados mockados por contexto:**
- **Estúdios de tatuagem**: Location em São Paulo/SP, team com "Tatuador Principal", "Artista Especialista", "Recepcionista"
- **Academias/CrossFit**: Location em cidade apropriada, team com "Personal Trainer", "Instrutor", "Nutricionista"
- **Clínicas/Consultórios**: Location em centro médico, team com "Dr./Dra. [Nome]", "Enfermeiro(a)", "Recepcionista"
- **Salões de beleza**: Location em centro comercial, team com "Cabeleireiro(a)", "Manicure", "Esteticista"
- **Restaurantes**: Location em área comercial, team com "Chef", "Garçom/Garçonete", "Gerente"

**Visual, design e usabilidade:**
1. Prefira tons claros para o background (próximos ao branco).
2. Garanta contraste adequado entre texto, botões e background.
3. Use cores fortes e contrastantes entre a cor dos botões (primary) e cor dos links dos botões (linkText).
4. Para itens sem imagens, use uma string vazia ("") no campo image
5. Use ícones apropriados do Font Awesome (fa fa-*) para cada tipo de link:
   - WhatsApp: fa fa-whatsapp
   - Instagram: fa fa-instagram
   - Facebook: fa fa-facebook
   - YouTube: fa fa-youtube
   - Email: fa fa-envelope
   - Localização: fa fa-map
   - Site/Blog: fa fa-globe
   - Telefone: fa fa-phone
   - Endereço: fa fa-map
   - Agendar: fa fa-calendar
   - Ver mais: fa fa-eye
   - Android: fa fa-googleplay
   - iOS: fa fa-apple

Input JSON:
{input_json}

Transforme estes dados em um formato otimizado para uma página de links mantendo a seguinte estrutura:
{format_instructions}

Lembre-se de:
- NUNCA inventar dados que não existam no JSON original
- Leve SEMPRE em consideração o contraste ao definir as cores dos botões, textos e background, garantindo a melhor experiència e usabilidade possível. Tome cuidado especial com cores claras como "amarelo".

**Informações adicionais para contexto:**
{additional_info}

**Instruções personalizadas:**

"""
