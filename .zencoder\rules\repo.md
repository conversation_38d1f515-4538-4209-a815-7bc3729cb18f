# LinkTreeScraper Information

## Summary

LinkTreeScraper is a Python-based tool for scraping Linktree user profiles. It extracts user information, links, and profile images, then processes and exports this data in JSON format. The tool also includes features for uploading profile images to ImgBB, extracting color palettes from images, and using AI to optimize data for link-in-bio pages.

## Structure

- **core/**: Contains constants and configuration values
- **models/**: Data models for Linktree users and links
- **services/**: Various services including image processing, AI transformation, and webhooks
- **output/**: Generated data for scraped Linktree profiles
- **web/**: HTML templates for displaying scraped data

## Language & Runtime

**Language**: Python
**Version**: 3.x (specific version not specified)
**Package Manager**: pip

## Dependencies

**Main Dependencies**:

- aiohttp: Asynchronous HTTP client/server
- beautifulsoup4: HTML parsing library
- requests: HTTP library
- Pillow: Image processing library
- pydantic: Data validation
- python-dotenv: Environment variable management
- colorthief: Color palette extraction
- tenacity: Retry library
- ratelimit: Rate limiting functionality

**AI & Integration Dependencies**:

- langchain: Framework for LLM applications
- langchain_openai: OpenAI integration for langchain
- langchain-google-genai: Google Gemini integration for langchain

## Build & Installation

```bash
git clone https://github.com/Touexe/LinkTreeScraper.git
pip install -r requirements.txt
```

## Usage

The tool can be used in two ways:

```bash
# Using username
python linktree.py <username>

# Using URL
python linktree.py https://linktr.ee/<username>
```

## Main Components

### Scraper

The core functionality is implemented in `linktree_scraper.py`, which handles:

- Fetching Linktree profile data
- Parsing HTML content with BeautifulSoup
- Extracting user information and links
- Handling censored/locked links

### Data Processing

- `data_handler.py`: Processes scraped data, integrates with AI services, and uploads to Firebase
- `json_exporter.py`: Exports data to JSON format
- `services/color_extractor.py`: Extracts color palettes from profile images

### AI Integration

The project uses AI models (OpenAI and Google Gemini) to optimize scraped data:

- `services/genai_service.py`: Transforms data using AI models
- Models are configured in `core/constants.py`

### Image Processing

- `services/image_downloader.py`: Downloads profile images
- `services/imgbb_service.py`: Uploads images to ImgBB
- `services/upload_avatar.py`: Handles avatar image processing

### External Integration

- `services/webhook_service.py`: Sends data to external webhooks
- Firebase integration for storing processed data

## Data Flow

1. User provides a Linktree username or URL
2. System scrapes the profile data using `LinktreeScraper`
3. Profile image is downloaded and processed
4. Color palette is extracted from the image
5. Data is transformed and optimized using AI models
6. Results are saved locally in the `output/` directory
7. Data is uploaded to Firebase and sent to webhooks

The project follows an asynchronous programming model using Python's `asyncio` library, allowing for efficient network operations when scraping multiple profiles.
