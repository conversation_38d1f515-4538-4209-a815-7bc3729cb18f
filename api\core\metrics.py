import time
import psutil
from typing import Dict, Any


class AppMetrics:
    """Classe para gerenciar métricas da aplicação"""

    def __init__(self):
        self.start_time = time.time()
        self.total_scrapes = 0
        self.active_connections = 0

    def increment_scrapes(self):
        """Incrementa o contador de scrapes"""
        self.total_scrapes += 1

    def increment_connections(self):
        """Incrementa o contador de conexões ativas"""
        self.active_connections += 1

    def decrement_connections(self):
        """Decrementa o contador de conexões ativas"""
        if self.active_connections > 0:
            self.active_connections -= 1

    def get_uptime(self) -> float:
        """Retorna o tempo de atividade em segundos"""
        return time.time() - self.start_time

    def get_memory_usage(self) -> float:
        """Retorna o uso de memória em MB"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss / 1024 / 1024  # Converter bytes para MB
        except Exception:
            return 0.0

    def get_all_metrics(self) -> Dict[str, Any]:
        """Retorna todas as métricas"""
        return {
            "total_scrapes": self.total_scrapes,
            "active_connections": self.active_connections,
            "uptime_seconds": self.get_uptime(),
            "memory_usage_mb": self.get_memory_usage(),
        }


# Instância global das métricas
app_metrics = AppMetrics()
