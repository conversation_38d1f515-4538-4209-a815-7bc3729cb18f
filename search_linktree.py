import requests
import json
import sys
import os
import subprocess
from datetime import datetime
import logging
from ratelimit import limits, sleep_and_retry
from tenacity import retry, stop_after_attempt, wait_exponential

from core.constants import FIREBASE_URL, SERPER_API_KEY

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

ONE_MINUTE = 60
MAX_CALLS_PER_MINUTE = 5  # SerperDev has strict rate limits


def create_result_folder(base_path, result):
    """Create a folder for each result and return the path."""
    from services.utils.normalize_username import normalize_username

    title = result.get("title", "unknown")
    normalized_title = normalize_username(title)
    folder_path = os.path.join("output", normalized_title)
    os.makedirs(folder_path, exist_ok=True)
    return folder_path


def save_individual_result(folder_path, result):
    """Save individual result as JSON file."""
    filename = os.path.join(folder_path, "data.json")
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    return filename


def check_user_exists(username):
    """Check if user already exists in Firebase database."""
    try:
        # Remove any 'linktr.ee/' prefix and get clean username
        clean_username = username.replace("https://", "").replace("linktr.ee/", "")

        # Make request to Firebase
        firebase_url = f"{FIREBASE_URL}/{clean_username}.json"
        response = requests.get(firebase_url)
        response.raise_for_status()

        # If response data is not None, user exists
        return response.json() is not None
    except requests.exceptions.RequestException as e:
        logger.error(f"Error checking Firebase for user {username}: {e}")
        return False


def process_linktree_result(result_folder, website_url):
    """Execute linktree.py script for the result using the same virtual environment."""
    try:
        # Check if user exists before processing
        if check_user_exists(website_url):
            logger.info(f"User already exists in Firebase, skipping: {website_url}")
            return

        subprocess.run([sys.executable, "linktree.py", website_url], check=True)
        logger.info(f"Processed Linktree profile: {website_url}")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error processing Linktree profile {website_url}: {e}")


@sleep_and_retry
@limits(calls=MAX_CALLS_PER_MINUTE, period=ONE_MINUTE)
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def search_linktree(term):
    all_results = {"places": []}

    # Create main output directory once
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_output_dir = os.path.join("output", f"{term.replace(' ', '_')}_{timestamp}")
    os.makedirs(base_output_dir, exist_ok=True)

    for page in range(1, 6):  # Pages 1-5
        try:
            url = "https://google.serper.dev/places"
            payload = json.dumps(
                {
                    "q": f"{term} linktr.ee",
                    "location": "Brazil",
                    "page": page,
                }
            )

            headers = {
                "X-API-KEY": SERPER_API_KEY,
                "Content-Type": "application/json",
            }

            logger.info(f"Searching for: {term} (page {page})")
            response = requests.post(url, headers=headers, data=payload)
            response.raise_for_status()

            # Process and filter results
            results = response.json()

            if "places" in results:
                filtered_results = []
                for place in results["places"]:
                    website = place.get("website", "")
                    if "linktr.ee" in website.lower():
                        # Create folder for this result
                        result_folder = create_result_folder(base_output_dir, place)

                        # Save individual result
                        save_individual_result(result_folder, place)

                        # Process Linktree profile
                        process_linktree_result(base_output_dir, website)

                        filtered_results.append(place)

                all_results["places"].extend(filtered_results)

        except requests.exceptions.RequestException as e:
            logger.error(f"Network error making request: {e}")
            continue
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON response: {e}")
            continue
        except Exception as e:
            logger.error(f"Unexpected error processing page {page}: {e}")
            continue

    # Save aggregated results after all pages are processed
    filename = os.path.join(base_output_dir, "all_results.json")
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)

    logger.info(f"Results saved to {filename}")
    logger.info(f"Found {len(all_results.get('places', []))} Linktree profiles")

    return filename


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python search_linktree.py <search_term>")
        sys.exit(1)

    try:
        search_term = sys.argv[1]
        output_file = search_linktree(search_term)
        print(f"Search completed successfully. Results saved to: {output_file}")
    except Exception as e:
        print(f"Error during search: {e}")
        sys.exit(1)
