from typing import Dict, Any
from langchain_google_genai import Cha<PERSON><PERSON><PERSON>gleGenerativeA<PERSON>
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langchain.prompts.chat import ChatPromptTemplate, HumanMessagePromptTemplate
from langchain.output_parsers import <PERSON><PERSON><PERSON>c<PERSON>utputParser
import json

from models.ppt_models import (
    GENERAL_PROMPT_TEMPLATE,
    TATTOO_PROMPT_TEMPLATE,
    BARBER_PROMPT_TEMPLATE,
)
from models.prompt_templates import TRANSFORM_PROMPT_TEMPLATE
from models.user_model import FirebaseData


# Template registry for dynamic selection
AVAILABLE_TEMPLATES = {
    "general": GENERAL_PROMPT_TEMPLATE,
    "tattoo": TATTOO_PROMPT_TEMPLATE,
    "barber": BARBER_PROMPT_TEMPLATE,
    "transform": TRANSFORM_PROMPT_TEMPLATE,
}

# Default template for backward compatibility
DEFAULT_TEMPLATE = "general"


def get_template_by_name(template_name: str = None) -> str:
    """
    Get template by name with validation.

    Args:
        template_name: Name of the template to retrieve

    Returns:
        The template string

    Raises:
        ValueError: If template name is invalid
    """
    if template_name is None:
        template_name = DEFAULT_TEMPLATE

    if template_name not in AVAILABLE_TEMPLATES:
        available = ", ".join(AVAILABLE_TEMPLATES.keys())
        raise ValueError(
            f"Invalid template name '{template_name}'. "
            f"Available templates: {available}"
        )

    return AVAILABLE_TEMPLATES[template_name]


def get_available_template_names() -> list[str]:
    """Get list of available template names."""
    return list(AVAILABLE_TEMPLATES.keys())


class AiDataTransformer:
    def __init__(self, gemini_api_key: str, openai_api_key: str):
        self.gemini_api_key = gemini_api_key
        self.openai_api_key = openai_api_key

    def create_transformer(self, model: str = "gemini") -> Any:
        """
        Create a transformer using either Gemini or OpenAI model.

        Args:
            model: Which model to use ("gemini" or "openai")

        Returns:
            The configured model
        """
        # Return the appropriate model
        return (
            ChatGoogleGenerativeAI(
                model="gemini-2.5-pro",
                # model="gemini-2.5-flash",
                google_api_key=self.gemini_api_key,
                temperature=0.2,
            )
            if model == "gemini"
            else ChatOpenAI(
                model="gpt-4o-mini", api_key=self.openai_api_key, temperature=0.1
            )
        )

    def transform(
        self,
        input_json: Dict[str, Any],
        model: str = "gemini",
        additional_info: str = "",
        template_name: str = None,
    ) -> Dict[str, Any]:
        """
        Transform JSON data using AI to optimize it for link-in-bio pages.

        Args:
            input_json: The JSON data to transform
            model: AI model to use ("gemini" or "openai")
            additional_info: Additional context information for processing
            template_name: Name of the template to use (general, tattoo, barber, transform)
        """
        try:
            # Get the template to use
            template = get_template_by_name(template_name)

            # Create parser and prompt
            parser = PydanticOutputParser(pydantic_object=FirebaseData)
            prompt = ChatPromptTemplate(
                messages=[HumanMessagePromptTemplate.from_template(template)],
                input_variables=["input_json", "additional_info"],
                partial_variables={
                    "format_instructions": parser.get_format_instructions()
                },
            )

            # Create AI model
            llm = self.create_transformer(model)

            # Prepare additional info text
            info_text = (
                additional_info
                if additional_info
                else "Nenhuma informação adicional fornecida."
            )

            # Format prompt with input JSON and additional info
            _input = prompt.format_prompt(
                input_json=json.dumps(input_json, ensure_ascii=False),
                additional_info=info_text,
            )

            # Get AI response
            output = llm.invoke(_input.to_messages())

            # Parse the response
            transformed_data = parser.parse(output.content)
            final_data = transformed_data.model_dump()

            # Ensure all required sections exist in each user profile
            if "users" in final_data:
                for username, profile in final_data["users"].items():
                    # Ensure empty lists/objects for missing sections
                    if "socialMedias" not in profile:
                        profile["socialMedias"] = []

                    if "links" not in profile:
                        profile["links"] = []

                    if "gallery" not in profile:
                        profile["gallery"] = {
                            "enabled": False,
                            "title": "",
                            "description": "",
                            "images": [],
                        }

                    if "genericSection" not in profile:
                        profile["genericSection"] = {
                            "enabled": False,
                            "items": [],
                            "description": "",
                            "title": "",
                        }

                    if "servicesSection" not in profile:
                        profile["servicesSection"] = {
                            "enabled": False,
                            "items": [],
                            "description": "",
                            "title": "",
                        }

                    if "featuresSection" not in profile:
                        profile["featuresSection"] = {
                            "enabled": False,
                            "title": "",
                            "description": "",
                            "items": [],
                        }

                    if "video" not in profile:
                        profile["video"] = {
                            "description": "",
                            "showVideo": False,
                            "title": "",
                            "youtubeId": "",
                            "youtubeUrl": "",
                        }

                    if "reviews" not in profile:
                        profile["reviews"] = {
                            "description": "",
                            "reviews": [],
                            "showReviews": False,
                            "title": "Avaliações",
                        }

                    if "settings" not in profile:
                        profile["settings"] = {
                            "colors": {
                                "background": "#ffffff",
                                "linkText": "#fff",
                                "primary": "#156882",
                                "secondary": "#646464",
                                "socialIconBackground": "#dddddd",
                            },
                            "favicon": "",
                            "ogImage": "",
                            "pageDescription": "",
                            "pageKeywords": "",
                        }

                    if "user" not in profile:
                        profile["user"] = {
                            "bio": "",
                            "name": username,
                            "avatar": "",
                            "brandLogo": "",
                            "heroImage": "",
                            "username": f"{username}",
                        }

                    # Ensure heroImage defaults to avatar if empty
                    if "user" in profile:
                        if (
                            "heroImage" not in profile["user"]
                            or not profile["user"]["heroImage"]
                        ):
                            profile["user"]["heroImage"] = profile["user"].get(
                                "avatar", ""
                            )

                    # Ensure brandLogo defaults to avatar if empty
                    if "user" in profile:
                        if (
                            "brandLogo" not in profile["user"]
                            or not profile["user"]["brandLogo"]
                        ):
                            profile["user"]["brandLogo"] = profile["user"].get(
                                "avatar", ""
                            )

                    # Ensure ogImage defaults to favicon if empty
                    if "settings" in profile:
                        if (
                            "ogImage" not in profile["settings"]
                            or not profile["settings"]["ogImage"]
                        ):
                            profile["settings"]["ogImage"] = profile["settings"].get(
                                "favicon", ""
                            )

                    # Ensure team has description field
                    if "team" in profile and profile["team"].get("enabled", False):
                        if "description" not in profile["team"]:
                            profile["team"]["description"] = ""

                    # Ensure model field is set with template_name value
                    model_value = template_name or "general"
                    profile["model"] = model_value
                    print(
                        f"[INFO] Set model field to '{model_value}' for user {username}"
                    )

            return final_data
        except Exception as e:
            print(f"Error parsing AI response: {e}")
            return input_json
