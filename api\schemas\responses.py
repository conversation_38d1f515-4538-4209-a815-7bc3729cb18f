from pydantic import BaseModel, Field
from typing import Optional, Any, Dict
from datetime import datetime
from models.link import Link


class HealthResponse(BaseModel):
    """Schema para resposta do health check"""

    status: str = Field(..., description="Status da API")
    timestamp: datetime = Field(
        default_factory=datetime.now, description="Timestamp da verificação"
    )
    version: str = Field("1.0.0", description="Versão da API")


class MetricsResponse(BaseModel):
    """Schema para resposta de métricas"""

    total_scrapes: int = Field(0, description="Total de scrapes realizados")
    active_connections: int = Field(0, description="Conexões ativas")
    uptime_seconds: float = Field(0.0, description="Tempo de atividade em segundos")
    memory_usage_mb: Optional[float] = Field(None, description="Uso de memória em MB")

    model_config = {
        "json_schema_extra": {
            "example": {
                "total_scrapes": 150,
                "active_connections": 5,
                "uptime_seconds": 3600.5,
                "memory_usage_mb": 256.7,
            }
        }
    }


class LinkResponse(BaseModel):
    """Schema para resposta de link"""

    url: Optional[str] = Field(None, description="URL do link")
    button_text: Optional[str] = Field(None, description="Texto do botão")

    @classmethod
    def from_link(cls, link: Link):
        """Converter objeto Link para LinkResponse"""
        return cls(url=link.url, button_text=link.button_text)


class ScrapeUserResponse(BaseModel):
    """Schema para resposta do scraping de usuário"""

    success: bool = Field(..., description="Indica se o scraping foi bem-sucedido")
    message: str = Field(..., description="Mensagem de status")
    data: Optional[Dict[str, Any]] = Field(None, description="Dados do usuário")
    timestamp: datetime = Field(
        default_factory=datetime.now, description="Timestamp do scraping"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "success": True,
                "message": "Scraping realizado com sucesso",
                "data": {
                    "username": "example_user",
                    "url": "https://linktr.ee/example_user",
                    "avatar_image": "https://example.com/avatar.jpg",
                    "id": 12345,
                    "tier": "free",
                    "isActive": True,
                    "description": "Exemplo de descrição",
                    "number_of_links": 5,
                    "main_colors": ["#ff0000", "#00ff00"],
                    "links": [
                        {"url": "https://example.com", "button_text": "Meu Site"}
                    ],
                },
                "timestamp": "2025-01-07T18:10:00Z",
            }
        }
    }


class ProcessCompleteResponse(BaseModel):
    """Schema para resposta do processo completo"""

    success: bool = Field(
        ..., description="Indica se o processo completo foi bem-sucedido"
    )
    message: str = Field(..., description="Mensagem de status do processo")
    data: Optional[Dict[str, Any]] = Field(
        None, description="Dados do usuário processados"
    )
    firebase_url: Optional[str] = Field(None, description="URL dos dados no Firebase")
    final_url: Optional[str] = Field(
        None,
        description="URL final da página do usuário (https://link.avenca.site/USERNAME)",
    )
    username: Optional[str] = Field(None, description="Username normalizado do usuário")
    processing_time_seconds: Optional[float] = Field(
        None, description="Tempo de processamento em segundos"
    )
    timestamp: datetime = Field(
        default_factory=datetime.now, description="Timestamp do processamento"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "success": True,
                "message": "Processo completo realizado com sucesso. Dados processados e enviados para Firebase.",
                "data": {
                    "username": "example_user",
                    "url": "https://linktr.ee/example_user",
                    "avatar_image": "https://example.com/avatar.jpg",
                    "id": 12345,
                    "tier": "free",
                    "isActive": True,
                    "description": "Exemplo de descrição",
                    "number_of_links": 5,
                    "createdAt": 1641555600000,
                    "updatedAt": 1641555600000,
                    "links": [
                        {"url": "https://example.com", "button_text": "Meu Site"}
                    ],
                },
                "firebase_url": "https://linktreescraper-default-rtdb.firebaseio.com/users/example_user.json",
                "final_url": "https://link.avenca.site/example_user",
                "username": "example_user",
                "processing_time_seconds": 12.5,
                "timestamp": "2025-07-13T03:40:00Z",
            }
        }
    }


class ErrorResponse(BaseModel):
    """Schema para resposta de erro"""

    error: str = Field(..., description="Tipo do erro")
    message: str = Field(..., description="Mensagem de erro")
    detail: Optional[str] = Field(None, description="Detalhes adicionais do erro")
    timestamp: datetime = Field(
        default_factory=datetime.now, description="Timestamp do erro"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "error": "ValidationError",
                "message": "Username inválido",
                "detail": "Username não pode ser vazio",
                "timestamp": "2025-01-07T18:10:00Z",
            }
        }
    }
