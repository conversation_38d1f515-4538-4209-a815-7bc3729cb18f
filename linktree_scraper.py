from typing import List, Union, Optional
import asyncio
import aiohttp
from bs4 import BeautifulSoup
import json
import os
import logging
from aiohttp import ClientTimeout
from tenacity import retry, stop_after_attempt, wait_exponential
from pathlib import Path
from models.linktree_user import LinktreeUser
from models.link import Link
from services.utils.normalize_username import normalize_username
from services.webhook_service import WebhookService

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class LinktreeScraper:
    def __init__(self):
        self.timeout = ClientTimeout(total=30)
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.webhook_service = WebhookService(
            webhook_url="https://db-core-n8n.vhhb1z.easypanel.host/webhook/userdata"
        )

    @retry(
        stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def _fetch(
        self, url: str, method: str = "GET", headers: dict = {}, data: dict = {}
    ) -> tuple[aiohttp.ClientSession, aiohttp.ClientResponse]:
        session = None
        try:
            merged_headers = {**self.headers, **headers}
            session = aiohttp.ClientSession(
                headers=merged_headers, timeout=self.timeout
            )
            resp = await session.request(method=method, url=url, json=data)
            resp.raise_for_status()
            return session, resp
        except aiohttp.ClientError as e:
            if session:
                await session.close()
            logger.error(f"Network error while fetching {url}: {str(e)}")
            raise
        except Exception as e:
            if session:
                await session.close()
            logger.error(f"Unexpected error while fetching {url}: {str(e)}")
            raise

    async def getSource(self, url: str):
        try:
            session, resp = await self._fetch(url)
            content = await resp.text()
            await session.close()
            return content
        except Exception as e:
            logger.error(f"Error getting source from {url}: {str(e)}")
            raise

    async def getUserInfoJSON(
        self, source=None, url: Optional[str] = None, username: Optional[str] = None
    ):
        if url is None and username:
            url = f"https://linktr.ee/{username}"

        if source is None and url:
            source = await self.getSource(url)

        soup = BeautifulSoup(source, "html.parser")
        attributes = {"id": "__NEXT_DATA__"}
        user_info = soup.find("script", attrs=attributes)
        user_data = json.loads(user_info.contents[0])["props"]["pageProps"]
        return user_data

    async def uncensorLinks(self, account_id: int, link_ids: Union[List[int], int]):
        if isinstance(link_ids, int):
            link_ids = [link_ids]
        
        # Se não há links para descesurar, retorna lista vazia
        if not link_ids:
            logger.info("No censored links to process")
            return []

        headers = {
            "origin": "https://linktr.ee",
            "referer": "https://linktr.ee",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36",
        }

        data = {
            "accountId": account_id,
            "validationInput": {"acceptedSensitiveContent": link_ids},
            "requestSource": {"referrer": None},
        }

        url = "https://linktr.ee/api/profiles/validation/gates"
        session, resp = await self._fetch(
            method="POST", url=url, headers=headers, data=data
        )

        json_resp = await resp.json()
        await session.close()

        _links = json_resp["links"]

        links = []
        for _link in _links:
            url = _link["url"]
            button_text = _link.get("title") or None
            link = Link(url=url, button_text=button_text)
            links.append(link)
        return links

    async def getUserLinks(
        self, username: Optional[str] = None, data: Optional[dict] = None
    ):
        if data is None and username:
            data = await self.getUserInfoJSON(username=username)

        user_id = data["account"]["id"]
        _links = data["links"]

        links = []
        censored_links_ids = []

        for _link in _links:
            id = int(_link["id"])
            url = _link["url"]
            locked = _link["locked"]

            button_text = _link.get("title") or _link.get("label") or None
            link = Link(url=url, button_text=button_text)
            if _link["type"] == "COMMERCE_PAY":
                continue

            if url is None and locked is True:
                censored_links_ids.append(id)
                continue
            links.append(link)

        if censored_links_ids:
            uncensored_links = await self.uncensorLinks(
                account_id=user_id, link_ids=censored_links_ids
            )
            links.extend(uncensored_links)

        return links

    async def getLinktreeUserInfo(
        self, url: Optional[str] = None, username: Optional[str] = None
    ) -> LinktreeUser:
        if url is None and username is None:
            print("Please pass linktree username or url.")
            return

        JSON_INFO = await self.getUserInfoJSON(url=url, username=username)
        account = JSON_INFO["account"]
        username = account["username"]
        avatar_image = account["profilePictureUrl"]
        url = f"https://linktr.ee/{username}" if url is None else url
        id = account["id"]
        tier = account.get("tier")
        isActive = account.get("isActive", False)
        createdAt = account.get("createdAt", 0)
        updatedAt = account.get("updatedAt", 0)
        description = account.get("description")

        links = await self.getUserLinks(data=JSON_INFO)

        user_info = LinktreeUser(
            username=username,
            url=url,
            avartar_image=avatar_image,
            id=id,
            tier=tier,
            isActive=isActive,
            createdAt=createdAt,
            updatedAt=updatedAt,
            description=description,
            links=links,
        )
        normalized_username = normalize_username(username)
        await self.generate_unified_json(user_info=user_info, folder_name=normalized_username)
        return user_info

    async def generate_unified_json(self, user_info: LinktreeUser, folder_name: str):
        """Gera um arquivo JSON unificado com todos os dados do usuário"""
        from services.color_extractor import ColorExtractor

        logger.info(f"Starting generate_unified_json for user: {user_info.username}")
        normalized_folder = normalize_username(folder_name)
        output_path = f"output/{normalized_folder}/{normalized_folder}.json"
        assets_dir = f"output/{normalized_folder}/assets"
        data_json_path = f"output/{normalized_folder}/data.json"
        os.makedirs(assets_dir, exist_ok=True)
        logger.info(f"Output path: {output_path}")

        # Load additional data from data.json if it exists
        additional_data = {}
        if os.path.exists(data_json_path):
            try:
                with open(data_json_path, "r", encoding="utf-8") as f:
                    additional_data = json.load(f)
            except Exception as e:
                logger.error(f"Error reading data.json: {e}")

        # Download and save avatar image
        # local_avatar_path = None
        if user_info.avartar_image:
            try:
                from services.image_downloader import download_image

                local_avatar_path = download_image(
                    user_info.avartar_image, Path(assets_dir), normalized_folder
                )
            except Exception as e:
                logger.error(f"Error downloading avatar: {e}")

        # Extract color palette
        main_colors = []
        if user_info.avartar_image:
            try:
                color_extractor = ColorExtractor()
                main_colors = color_extractor.get_main_colors(user_info.avartar_image)
            except Exception as e:
                logger.error(f"Error extracting colors: {e}")

        # Try to get uploaded avatar URL if available
        avatar_url = user_info.avartar_image  # Default to original URL
        avatar_json_path = f"output/{normalized_folder}/avatar_{normalized_folder}.json"
        try:
            if os.path.exists(avatar_json_path):
                with open(avatar_json_path, "r") as f:
                    avatar_data = json.load(f)
                    if "avatar_url" in avatar_data:
                        avatar_url = avatar_data["avatar_url"]
        except Exception as e:
            logger.error(f"Error reading avatar JSON: {e}")

        normalized_username = normalize_username(user_info.username)
        # Create base user data
        user_data = {
            "username": normalized_username,
            "url": user_info.url,
            "avatar_image": avatar_url,
            "id": user_info.id,
            "tier": user_info.tier,
            "isActive": user_info.isActive,
            "description": user_info.description,
            "createdAt": user_info.createdAt,
            "updatedAt": user_info.updatedAt,
            "number_of_links": len(user_info.links),
            "main_colors": main_colors,
            "links": [link.__dict__ for link in user_info.links],
        }

        # Merge additional data from data.json if it exists
        if additional_data:
            user_data = {**user_data, **additional_data}

        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        logger.info(f"Writing JSON to: {output_path}")
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(user_data, f, indent=4, ensure_ascii=False)
        logger.info(f"JSON file successfully created at: {output_path}")

        # Send data to webhook
        await self.webhook_service.send_to_webhook(user_data)


async def main(url: str):
    print(f"[+] Iniciando scraping do perfil: {url}")
    scraper = LinktreeScraper()
    print("[+] Coletando informações do perfil...")
    user_info = await scraper.getLinktreeUserInfo(url=url)
    print(f"[+] Scraping concluído com sucesso!")
    normalized_username = normalize_username(user_info.username)
    print(
        f"[+] Arquivo JSON gerado em: output/{normalized_username}/{normalized_username}.json"
    )


if __name__ == "__main__":
    import sys

    if len(sys.argv) != 2:
        print("Usage: python scraper.py <linktree_url>")
        sys.exit(1)

    url = sys.argv[1]
    asyncio.run(main(url))
