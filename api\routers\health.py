from fastapi import APIRouter, Depends
from api.schemas.responses import HealthResponse, MetricsResponse
from api.schemas.requests import MetricsRequest
from api.core.metrics import app_metrics
from api.core.config import settings

router = APIRouter(tags=["Health"])


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    Endpoint para verificação de saúde da API
    
    Retorna o status atual da API, versão e timestamp.
    """
    return HealthResponse(
        status="healthy",
        version=settings.app_version
    )


@router.get("/metrics", response_model=MetricsResponse)
async def get_metrics():
    """
    Endpoint para obter métricas básicas da aplicação
    
    Retorna informações sobre:
    - Total de scrapes realizados
    - Conexões ativas
    - Tempo de atividade
    - Uso de memória
    """
    metrics_data = app_metrics.get_all_metrics()
    
    return MetricsResponse(
        total_scrapes=metrics_data["total_scrapes"],
        active_connections=metrics_data["active_connections"],
        uptime_seconds=metrics_data["uptime_seconds"],
        memory_usage_mb=metrics_data["memory_usage_mb"]
    )
