import requests
from typing import Optional


class ImgBBService:
    def __init__(self, api_key: str = "efd2d9281586d0efb6a45b374bfaddbd"):
        self.api_key = api_key
        self.upload_url = "https://api.imgbb.com/1/upload"

    async def upload_image(
        self, image_data: str, name: Optional[str] = None
    ) -> Optional[str]:
        """
        Uploads an image to ImgBB and returns the direct URL

        Args:
            image_data: Base64 encoded image data or image URL
            name: Optional name for the image

        Returns:
            str: Direct URL to the uploaded image or None if failed
        """
        print("[INFO] Starting image upload to ImgBB...")

        try:
            payload = {"key": self.api_key, "image": image_data}

            if name:
                payload["name"] = name
                print(f"[INFO] Using custom image name: {name}")

            print("[INFO] Sending request to ImgBB API...")
            response = requests.post(self.upload_url, data=payload)
            response.raise_for_status()

            json_data = response.json()
            print("[INFO] Received response from ImgBB API")

            if json_data.get("success"):
                image_url = json_data["data"]["url"]
                print(f"[SUCCESS] Image uploaded successfully. URL: {image_url}")
                return image_url

            print("[ERROR] ImgBB API returned unsuccessful response")
            return None

        except requests.exceptions.RequestException as e:
            print(f"[ERROR] Network error during ImgBB upload: {str(e)}")
            return None
        except Exception as e:
            print(f"[ERROR] Failed to upload image to ImgBB: {str(e)}")
            return None
