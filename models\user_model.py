from typing import List, Dict
from pydantic import BaseModel, Field


class Button(BaseModel):
    icon: str = Field(description="Ícone do botão (ex: fa fa-eye)")
    url: str = Field(description="URL do botão")


class SettingsColors(BaseModel):
    background: str = Field(description="Cor de fundo")
    linkText: str = Field(description="Cor do texto nos botões")
    primary: str = Field(description="Cor primária do botão")
    secondary: str = Field(description="Cor secundária")
    socialIconBackground: str = Field(description="Cor de fundo dos ícones sociais")


class Settings(BaseModel):
    favicon: str = Field(description="URL do favicon")
    ogImage: str = Field(
        description="URL da imagem Open Graph (padrão: mesmo valor do favicon)"
    )
    pageDescription: str = Field(description="Descrição da página")
    pageKeywords: str = Field(description="Palavras-chave da página")
    colors: SettingsColors = Field(description="Configurações de cores")


class User(BaseModel):
    name: str = Field(description="Nome do usuário")
    username: str = Field(description="Nome de username")
    bio: str = Field(description="Biografia do usuário")
    avatar: str = Field(description="URL da foto do perfil")
    heroImage: str = Field(
        description="URL da imagem de background do Hero (padrão: mesmo valor do avatar)"
    )
    brandLogo: str = Field(
        description="URL do logo da marca (padrão: mesmo valor do avatar)"
    )


class Video(BaseModel):
    enabled: bool = Field(description="Se o vídeo deve ser exibido")
    title: str = Field(description="Título do vídeo")
    description: str = Field(description="Descrição do vídeo")
    youtubeUrl: str = Field("", description="URL completa do vídeo no YouTube")


class Image(BaseModel):
    id: int = Field(description="ID único da imagem")
    title: str = Field(description="Título da imagem")
    description: str = Field(description="Descrição da imagem")
    alt: str = Field(description="Texto alternativo da imagem")
    url: str = Field(description="URL da imagem")


class Gallery(BaseModel):
    enabled: bool = Field(description="Se a galeria deve ser exibida")
    title: str = Field(description="Título da seção da galeria")
    description: str = Field(description="Descrição da galeria")
    images: List[Image] = Field(description="Lista de imagens")


class GenericSectionItem(BaseModel):
    id: int = Field(description="ID único do item")
    title: str = Field(description="Título do item")
    description: str = Field(description="Descrição do item")
    image: str = Field(description="URL da imagem do item")
    primaryButton: Button = Field(description="Botão principal")
    secondaryButton: Button = Field(description="Botão secundário")


class GenericSection(BaseModel):
    enabled: bool = Field(description="Se a seção está habilitada")
    title: str = Field(description="Título da seção")
    description: str = Field(description="Subtítulo da seção")
    items: List[GenericSectionItem] = Field(description="Lista de itens")


class Link(BaseModel):
    text: str = Field(description="Texto do link")
    classIcon: str = Field(description="Classe do ícone (ex: fa fa-whatsapp)")
    url: str = Field(description="URL do link")


class Review(BaseModel):
    comment: str = Field(description="Comentário do cliente")
    id: int = Field(description="ID único da avaliação")
    name: str = Field(description="Nome do cliente")
    photo: str = Field(description="URL da foto do cliente")
    rating: int = Field(description="Avaliação (1-5)")


class Reviews(BaseModel):
    enabled: bool = Field(description="Se as avaliações devem ser exibidas")
    title: str = Field("Nossas avaliações", description="Título da seção de avaliações")
    description: str = Field(
        "O que nossos alunos dizem sobre nós",
        description="Descrição da seção de avaliações",
    )
    reviews: List[Review] = Field(description="Lista de avaliações")


class ServicesSection(BaseModel):
    enabled: bool = Field(description="Se a seção está habilitada")
    items: List[GenericSectionItem] = Field(description="Lista de itens")
    title: str = Field(description="Título da seção")
    description: str = Field("", description="Descrição da seção")


class FeaturesSection(BaseModel):
    enabled: bool = Field(description="Se a seção está habilitada")
    items: List[GenericSectionItem] = Field(description="Lista de itens")
    title: str = Field(description="Título da seção")
    description: str = Field("", description="Descrição da seção")


class LocationAddress(BaseModel):
    city: str = Field(description="Cidade")
    country: str = Field(description="País")
    state: str = Field(description="Estado/UF")
    street: str = Field(description="Endereço completo da rua")
    zipCode: str = Field(description="CEP")


class LocationContact(BaseModel):
    phone: str = Field(description="Número de telefone")
    whatsapp: str = Field(description="URL do WhatsApp")


class LocationHours(BaseModel):
    closed: str = Field(description="Dias fechados")
    weekdays: str = Field(description="Horário de funcionamento nos dias úteis")
    weekends: str = Field(description="Horário de funcionamento nos fins de semana")


class Location(BaseModel):
    enabled: bool = Field(
        description="Se as informações de localização estão habilitadas"
    )
    address: LocationAddress = Field(description="Endereço completo")
    contact: LocationContact = Field(description="Informações de contato")
    googleMapsUrl: str = Field(description="URL do Google Maps")
    hours: LocationHours = Field(description="Horários de funcionamento")


class TeamMember(BaseModel):
    name: str = Field(description="Nome do membro da equipe")
    role: str = Field(description="Especialidade, cargo ou função do membro")
    photo: str = Field(description="URL da foto do membro")
    url: str = Field(description="URL do perfil ou rede social do membro")


class Team(BaseModel):
    enabled: bool = Field(description="Se a seção de equipe está habilitada")
    title: str = Field(description="Título da seção de equipe")
    description: str = Field(description="Descrição da seção de equipe")
    members: List[TeamMember] = Field(description="Lista de membros da equipe")


class UserProfile(BaseModel):
    user: User = Field(description="Informações do usuário")
    links: List[Link] = Field(description="Lista de links principais")
    socialMedia: List[Link] = Field(description="Lista de redes sociais")
    gallery: Gallery = Field(description="Seção de galeria")
    genericSection: GenericSection = Field(description="Seção genérica")
    servicesSection: ServicesSection = Field(description="Seção de serviços/produtos")
    featuresSection: FeaturesSection = Field(description="Seção de features")
    reviews: Reviews = Field(description="Seção de avaliações")
    video: Video = Field(description="Seção de vídeo")
    settings: Settings = Field(description="Configurações visuais")
    location: Location = Field(description="Informações de localização e contato")
    team: Team = Field(description="Seção da equipe")
    phone: str = Field(
        description="Número de telefone extraído da URL do WhatsApp e formatado"
    )
    model: str = Field(
        default="general",
        description="Nome do template/modelo usado para gerar este perfil. Valores possíveis: general, tattoo, barber",
    )


class FirebaseData(BaseModel):
    users: Dict[str, UserProfile] = Field(
        description="Mapa de perfis de usuário por username"
    )
