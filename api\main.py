from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from starlette.middleware.cors import CORSMiddleware
from api.core.config import settings
from api.core.metrics import app_metrics
from api.routers import health, scraper


app = FastAPI(title=settings.app_name, version=settings.app_version, debug=settings.debug)

# Configuração do CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers,
)


@app.middleware("http")
async def add_metrics_middleware(request: Request, call_next):
    """Middleware para incrementar/decrementar conexões ativas"""
    app_metrics.increment_connections()
    try:
        response = await call_next(request)
    finally:
        app_metrics.decrement_connections()
    return response


# Inclusão dos routers
app.include_router(health.router, prefix="/api/v1")
app.include_router(scraper.router, prefix="/api/v1")
