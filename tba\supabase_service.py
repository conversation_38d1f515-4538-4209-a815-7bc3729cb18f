# import os
# from datetime import datetime
# import requests
# from io import BytesIO
# from supabase import create_client
# from pathlib import Path
# from dotenv import load_dotenv

# # Load environment variables
# load_dotenv()

# # Supabase configuration
# supabase_url = os.getenv("SUPABASE_URL")
# supabase_key = os.getenv("SUPABASE_KEY")
# SUPABASE_BUCKET = "avatars"  # The bucket name where we'll store avatars

# if not supabase_url or not supabase_key:
#     raise ValueError("Missing Supabase credentials. Please set SUPABASE_URL and SUPABASE_KEY in .env file")

# # Initialize Supabase client
# supabase = create_client(supabase_url, supabase_key)

# async def upload_avatar_to_supabase(avatar_path: str, username: str) -> str:
#     """
#     Takes a local avatar file path, converts it to webp, and uploads it to Supabase storage.
#     Returns the public URL of the uploaded image.
#     """
#     try:
#         print(f"[INFO] Starting avatar upload process for {username}")
#         print(f"[INFO] Source avatar path: {avatar_path}")
#         start_time = datetime.now()

#         # Check if the path is local
#         if avatar_path.startswith(('output', '.', '/', '\\')):
#             # Convert backslashes to forward slashes for consistency
#             avatar_path = avatar_path.replace('\\', '/')

#             # Handle absolute and relative paths
#             if not os.path.isabs(avatar_path):
#                 avatar_path = os.path.join(os.getcwd(), avatar_path)

#             # Check if file exists
#             if not os.path.exists(avatar_path):
#                 print(f"[WARNING] Avatar file not found: {avatar_path}")
#                 return avatar_path

#             print(f"[INFO] Reading local avatar file: {avatar_path}")
#             file_size = os.path.getsize(avatar_path)
#             print(f"[INFO] Local file size: {file_size} bytes")
#             with open(avatar_path, 'rb') as f:
#                 image_data = f.read()
#         else:
#             # Download from URL
#             print(f"[INFO] Downloading avatar from URL: {avatar_path}")
#             download_start = datetime.now()
#             response = requests.get(avatar_path)
#             response.raise_for_status()
#             image_data = response.content

#         # Convert to webp using Pillow
#         from PIL import Image
#         print(f"[INFO] Starting image conversion to WEBP format")
#         img = Image.open(BytesIO(image_data))
#         print(f"[INFO] Original image format: {img.format}, size: {img.size}")
#         webp_buffer = BytesIO()
#         img.save(webp_buffer, format="WEBP", quality=85, optimize=True)
#         print(f"[INFO] WEBP conversion complete. Output size: {len(webp_buffer.getvalue())} bytes")
#         webp_data = webp_buffer.getvalue()

#         # Generate a unique filename
#         timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#         filename = f"{username}_avatar_{timestamp}.webp"

#         # Ensure bucket exists and is public
#         try:
#             buckets = supabase.storage.list_buckets()
#             bucket_exists = any(bucket.name == SUPABASE_BUCKET for bucket in buckets)

#             if not bucket_exists:
#                 print(f"[INFO] Creating new bucket: {SUPABASE_BUCKET}")
#                 bucket_start = datetime.now()
#                 supabase.storage.create_bucket(
#                     SUPABASE_BUCKET,
#                     options={'public': True}
#                 )

#             # Delete old avatars
#             files = supabase.storage.from_(SUPABASE_BUCKET).list()
#             for file in files:
#                 if isinstance(file, dict) and 'name' in file and file['name'].startswith(f"{username}_avatar_"):
#                     print(f"[INFO] Removing old avatar: {file['name']}")
#                     print(f"[INFO] Found {len(files)} existing files for user {username}")
#                     supabase.storage.from_(SUPABASE_BUCKET).remove([file['name']])

#         except Exception as e:
#             print(f"[WARNING] Error managing bucket: {str(e)}")

#         # Upload to Supabase
#         print(f"[INFO] Uploading avatar to Supabase: {filename}")
#         upload_start = datetime.now()

#         # Upload new avatar
#         result = supabase.storage.from_(SUPABASE_BUCKET).upload(
#             path=filename,
#             file=webp_data,
#             file_options={"contentType": "image/webp"}
#         )
#         upload_duration = (datetime.now() - upload_start).total_seconds()
#         print(f"[INFO] Upload completed in {upload_duration:.2f} seconds")
#         print(f"[INFO] Upload result: {result}")

#         # Get the public URL
#         public_url = supabase.storage.from_(SUPABASE_BUCKET).get_public_url(filename)
#         total_duration = (datetime.now() - start_time).total_seconds()
#         print(f"[INFO] Avatar uploaded successfully. Public URL: {public_url}")
#         print(f"[INFO] Total upload process time: {total_duration:.2f} seconds")
#         return public_url

#     except Exception as e:
#         print(f"[ERROR] Error uploading avatar for {username}: {str(e)}")
#         print(f"[ERROR] Full error details: {repr(e)}")
#         return avatar_path  # Return original path if upload fails
