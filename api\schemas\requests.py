from pydantic import BaseModel, Field, field_validator
from typing import Optional
from services.utils.user_parser import parse_user_input, validate_info_parameter
from services.genai_service import get_available_template_names


class ScrapeUserRequest(BaseModel):
    """Schema para requisição de scraping de usuário"""

    user: str = Field(
        ...,
        description="Identificador do usuário Linktree (username, URL completa, ou linktr.ee URL)",
        min_length=1,
        max_length=200,
    )
    info: Optional[str] = Field(
        None,
        description="Informações adicionais para processamento e otimização dos dados",
        max_length=5000,
    )
    template_name: Optional[str] = Field(
        None,
        description="Nome do template a ser usado para processamento (general, tattoo, barber, transform). Padrão: general",
        max_length=50,
    )

    # Internal fields populated by validation
    _username: Optional[str] = None
    _url: Optional[str] = None

    @field_validator("user")
    @classmethod
    def parse_and_validate_user(cls, v: str) -> str:
        """Validar e processar o parâmetro user"""
        if not v or not v.strip():
            raise ValueError("Parâmetro 'user' não pode ser vazio")

        try:
            # Parse the user input to extract username and URL
            username, url = parse_user_input(v)
            # Store the parsed values (will be used by the service)
            return v  # Return original input, parsed values stored separately
        except ValueError as e:
            raise ValueError(f"Formato de usuário inválido: {str(e)}")

    @field_validator("info")
    @classmethod
    def validate_info_field(cls, v: Optional[str]) -> Optional[str]:
        """Validar parâmetro info"""
        if v is None:
            return v

        try:
            return validate_info_parameter(v)
        except ValueError as e:
            raise ValueError(f"Parâmetro 'info' inválido: {str(e)}")

    @field_validator("template_name")
    @classmethod
    def validate_template_name(cls, v: Optional[str]) -> Optional[str]:
        """Validar parâmetro template_name"""
        if v is None:
            return v

        # Get available templates
        available_templates = get_available_template_names()

        if v not in available_templates:
            available = ", ".join(available_templates)
            raise ValueError(
                f"Template '{v}' inválido. Templates disponíveis: {available}"
            )

        return v

    def get_parsed_user_data(self) -> tuple[str, str]:
        """Obter dados do usuário processados (username, url)"""
        return parse_user_input(self.user)


class MetricsRequest(BaseModel):
    """Schema para requisição de métricas"""

    detailed: Optional[bool] = Field(False, description="Retornar métricas detalhadas")
