from dataclasses import dataclass, field
from typing import List, Optional
from .link import Link

@dataclass
class LinktreeUser:
    username: str
    url: Optional[str]
    avartar_image: Optional[str]
    id: int
    tier: Optional[str] = None
    isActive: bool = False
    description: Optional[str] = None
    createdAt: int = 0
    updatedAt: int = 0
    links: List[Link] = field(default_factory=list)
