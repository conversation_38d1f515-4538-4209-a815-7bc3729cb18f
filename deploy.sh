#!/bin/bash

# Script de Deploy para LinkTree Scraper API
# Autor: Desenvolvido para deploy em VPS

set -e  # Sair em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log com cores
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# Verificar se Docker está instalado
check_docker() {
    if ! command -v docker &> /dev/null; then
        error "Docker não está instalado. Por favor, instale o Docker primeiro."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        error "Docker Compose não está instalado. Por favor, instale o Docker Compose primeiro."
        exit 1
    fi
}

# Verificar se o arquivo .env existe
check_env() {
    if [ ! -f .env ]; then
        warn "Arquivo .env não encontrado. Criando a partir do .env.example..."
        if [ -f .env.example ]; then
            cp .env.example .env
            log "Arquivo .env criado. Por favor, edite-o com suas configurações."
            read -p "Pressione Enter para continuar após editar o .env..."
        else
            error "Arquivo .env.example não encontrado."
            exit 1
        fi
    fi
}

# Parar containers existentes
stop_containers() {
    log "Parando containers existentes..."
    docker-compose down --remove-orphans || docker compose down --remove-orphans || true
}

# Construir imagens
build_images() {
    log "Construindo imagens Docker..."
    docker-compose build --no-cache || docker compose build --no-cache
}

# Iniciar serviços
start_services() {
    log "Iniciando serviços..."
    docker-compose up -d || docker compose up -d
}

# Verificar status dos serviços
check_services() {
    log "Verificando status dos serviços..."
    sleep 10
    
    # Verificar se a API está respondendo
    if curl -f http://localhost:8000/api/v1/health &> /dev/null; then
        log "✅ API está rodando corretamente!"
    else
        error "❌ API não está respondendo. Verificando logs..."
        docker-compose logs linktree-api || docker compose logs linktree-api
        exit 1
    fi
    
    # Verificar Redis
    if docker-compose ps redis | grep -q "Up" || docker compose ps redis | grep -q "Up"; then
        log "✅ Redis está rodando corretamente!"
    else
        warn "⚠️  Redis pode não estar funcionando corretamente."
    fi
}

# Mostrar informações de acesso
show_info() {
    header "DEPLOY CONCLUÍDO"
    log "🚀 LinkTree Scraper API foi deployada com sucesso!"
    echo ""
    log "📡 Endpoints disponíveis:"
    log "   - API: http://localhost:8000"
    log "   - Documentação: http://localhost:8000/docs"
    log "   - Health Check: http://localhost:8000/api/v1/health"
    echo ""
    log "🔍 Para ver os logs:"
    log "   docker-compose logs -f"
    echo ""
    log "🛑 Para parar os serviços:"
    log "   docker-compose down"
    echo ""
    log "🔄 Para reiniciar:"
    log "   docker-compose restart"
}

# Função principal
main() {
    header "DEPLOY LINKTREE SCRAPER API"
    
    check_docker
    check_env
    stop_containers
    build_images
    start_services
    check_services
    show_info
}

# Verificar se está sendo executado como script principal
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
