# 🚀 Deploy Guide - LinkTree Scraper API

Este guia contém instruções completas para fazer deploy da LinkTree Scraper API em uma VPS.

## 📋 Pré-requisitos

### Na VPS:
- Docker instalado (versão 20.10+)
- Docker Compose instalado
- Git instalado
- Pelo menos 2GB de RAM
- 10GB de espaço livre em disco

### Portas necessárias:
- **8000**: API FastAPI
- **6379**: Redis (opcional, apenas se exposto)
- **80**: HTTP (Nginx)
- **443**: HTTPS (Nginx)

## 🛠️ Instalação do Docker (Ubuntu/Debian)

```bash
# Atualizar sistema
sudo apt update && sudo apt upgrade -y

# Instalar dependências
sudo apt install apt-transport-https ca-certificates curl gnupg lsb-release -y

# Adicionar chave GPG do Docker
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Adicionar repositório
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Instalar Docker
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io docker-compose-plugin -y

# Adicionar usuário ao grupo docker
sudo usermod -aG docker $USER

# Reiniciar sessão ou executar:
newgrp docker
```

## 📦 Deploy Automático

### 1. Clone o repositório na VPS:
```bash
git clone https://github.com/gzpaitch/linktree-lead.git
cd linktree-lead
```

### 2. Configure as variáveis de ambiente:
```bash
# Copiar arquivo de exemplo
cp .env.example .env

# Editar configurações
nano .env
```

### 3. Execute o script de deploy:
```bash
# Dar permissão de execução
chmod +x deploy.sh

# Executar deploy
./deploy.sh
```

## ⚙️ Deploy Manual

### 1. Configurar variáveis de ambiente:
```bash
cp .env.example .env
nano .env
```

Configurações importantes no `.env`:
```env
LINKTREE_HOST=0.0.0.0
LINKTREE_PORT=8000
LINKTREE_DEBUG=false
LINKTREE_REDIS_URL=redis://redis:6379/0
```

### 2. Build e start dos containers:
```bash
# Build das imagens
docker-compose build

# Iniciar serviços
docker-compose up -d
```

### 3. Verificar status:
```bash
# Ver containers rodando
docker-compose ps

# Ver logs
docker-compose logs -f
```

## 🔧 Configuração do Nginx (Opcional)

Se você quiser usar um Nginx externo ao invés do container:

### 1. Instalar Nginx:
```bash
sudo apt install nginx -y
```

### 2. Configurar proxy reverso:
```bash
sudo nano /etc/nginx/sites-available/linktree-api
```

Exemplo de configuração:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. Ativar configuração:
```bash
sudo ln -s /etc/nginx/sites-available/linktree-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 SSL/HTTPS com Let's Encrypt

### 1. Instalar Certbot:
```bash
sudo apt install certbot python3-certbot-nginx -y
```

### 2. Gerar certificado:
```bash
sudo certbot --nginx -d your-domain.com
```

## 📊 Monitoramento

### Ver logs em tempo real:
```bash
docker-compose logs -f linktree-api
```

### Ver status dos containers:
```bash
docker-compose ps
```

### Ver uso de recursos:
```bash
docker stats
```

### Health check:
```bash
curl http://localhost:8000/api/v1/health
```

## 🔧 Comandos Úteis

### Reiniciar aplicação:
```bash
docker-compose restart linktree-api
```

### Parar todos os serviços:
```bash
docker-compose down
```

### Rebuild e reiniciar:
```bash
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Ver logs de um serviço específico:
```bash
docker-compose logs -f linktree-api
docker-compose logs -f redis
docker-compose logs -f nginx
```

### Atualizar código:
```bash
git pull origin main
docker-compose build --no-cache linktree-api
docker-compose up -d
```

## 🚨 Troubleshooting

### API não está respondendo:
```bash
# Verificar logs
docker-compose logs linktree-api

# Verificar se a porta está aberta
netstat -tlnp | grep 8000

# Reiniciar container
docker-compose restart linktree-api
```

### Problemas de memória:
```bash
# Ver uso de memória
docker stats

# Limpar imagens não utilizadas
docker system prune -a
```

### Redis não está conectando:
```bash
# Verificar logs do Redis
docker-compose logs redis

# Testar conexão
docker-compose exec redis redis-cli ping
```

### Problemas de permissão:
```bash
# Verificar ownership dos arquivos
ls -la

# Corrigir permissões se necessário
sudo chown -R $USER:$USER .
```

## 📈 Otimizações de Produção

### 1. Configurar limite de recursos:
Editar `docker-compose.yml`:
```yaml
services:
  linktree-api:
    # ... outras configurações
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "0.5"
        reservations:
          memory: 512M
          cpus: "0.25"
```

### 2. Configurar logging:
```yaml
services:
  linktree-api:
    # ... outras configurações
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### 3. Backup do Redis:
```bash
# Backup manual
docker-compose exec redis redis-cli BGSAVE

# Configurar backup automático via cron
0 2 * * * docker-compose exec redis redis-cli BGSAVE
```

## 🔐 Segurança

### 1. Firewall:
```bash
# Permitir apenas portas necessárias
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 2. Atualizações regulares:
```bash
# Atualizar sistema
sudo apt update && sudo apt upgrade -y

# Atualizar containers
docker-compose pull
docker-compose up -d
```

### 3. Monitoramento de logs:
```bash
# Configurar logrotate para logs do Docker
sudo nano /etc/logrotate.d/docker-container
```

## 📞 Suporte

Se encontrar problemas durante o deploy:

1. Verifique os logs: `docker-compose logs -f`
2. Verifique o status: `docker-compose ps`
3. Teste a conectividade: `curl http://localhost:8000/api/v1/health`
4. Verifique recursos do sistema: `htop`, `df -h`

## 🎯 Endpoints Principais

Após o deploy bem-sucedido:

- **API Base**: `http://your-server:8000`
- **Documentação**: `http://your-server:8000/docs`
- **Health Check**: `http://your-server:8000/api/v1/health`
- **Scraper**: `http://your-server:8000/api/v1/scraper/profile`

---

✅ **Deploy concluído com sucesso!** Sua API LinkTree Scraper está rodando em produção.
