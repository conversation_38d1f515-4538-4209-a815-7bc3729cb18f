Create a Streamlit web interface with the following specific components and functionality:

**Required Interface Elements:**

1. **URL Input Field**: A text input field where users can enter a Linktree URL for scraping
2. **Prompt Template Selector**: A dropdown/selectbox component allowing users to choose from these exact prompt templates:
   - GENERAL_PROMPT_TEMPLATE
   - BARBER_PROMPT_TEMPLATE
   - TATTOO_PROMPT_TEMPLATE
3. **Additional Information Field**: A text area input where users can optionally enter extra information that will be incorporated into the final result generation

**Technical Requirements:**

- Use Streamlit framework for the web interface
- Import the prompt templates from the existing `models/prompt_templates.py` file
- Ensure the interface integrates with the existing LinkTree scraping functionality
- The additional information field should be optional (users can leave it empty)
- Include proper labels and descriptions for each input field
- Add a submit/process button to trigger the scraping and content generation

**Functionality:**

- When submitted, the interface should use the selected prompt template combined with any additional user-provided information to generate the final result
- Follow the existing codebase patterns for data processing and prompt handling
- Ensure the interface is user-friendly with clear instructions for each field

**Integration Notes:**

- Maintain compatibility with the existing Pydantic models and data structures
- Use the same comprehensive pattern established in the codebase for handling different content types
- Ensure proper error handling for invalid URLs or processing failures
