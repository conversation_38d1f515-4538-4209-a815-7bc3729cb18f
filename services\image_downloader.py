from pathlib import Path
from typing import Optional
import requests
from PIL import Image
from io import BytesIO
import os
from ratelimit import limits, sleep_and_retry

ONE_MINUTE = 60
MAX_CALLS_PER_MINUTE = 30

@sleep_and_retry
@limits(calls=MAX_CALLS_PER_MINUTE, period=ONE_MINUTE)
def download_image(image_url: str, save_dir: Path, username: str) -> Optional[str]:
    """Download and save image from URL with username-based naming"""
    try:
        response = requests.get(image_url)
        response.raise_for_status()

        # Create a PIL Image from the response content
        img = Image.open(BytesIO(response.content))
        
        # Convert to WebP format
        webp_path = save_dir / f"avatar_{username}.webp"
        img.save(str(webp_path), 'WEBP', quality=80)
        
        # Also save original for backup
        original_ext = os.path.splitext(image_url)[1] or ".jpg"
        original_path = save_dir / f"avatar_{username}_original{original_ext}"
        with open(original_path, "wb") as f:
            f.write(response.content)

        return str(webp_path)
    except requests.RequestException as e:
        print(f"Network error downloading image: {e}")
        return None
    except IOError as e:
        print(f"Error processing image: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None
