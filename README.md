# LinkTreeScraper

Scraping Linktree user's information and links using python.

# Installation

install project

`git clone https://github.com/Touexe/LinkTreeScraper.git `

install requirements

`pip install requirements.txt`

# Usage

`linktree.py <username> or <url>`

# Example

`linktree.py Pale_but_peachy`

`linktree.py https://linktr.ee/Pale_but_peachy`

# Project Structure

LinkTreeScraper/
├── src/
│ ├── scraper/ # Código principal do scraper
│ │ ├── **init**.py
│ │ ├── scraper.py # Lógica principal do scraping
│ │ └── linktree.py # Manipulação específica do Linktree
│ ├── generators/ # Geradores de páginas e exportadores
│ │ ├── **init**.py
│ │ ├── page_generator.py # Geração de páginas HTML
│ │ └── json_exporter.py # Exportação para JSON
│ ├── templates/ # Templates HTML
│ │ ├── base.html # Template base
│ │ ├── linktree.html # Template específico do Linktree
│ │ └── link_proposal.html # Template de proposta
│ └── utils/ # Utilitários e helpers
│ ├── **init**.py
│ └── file_utils.py # Funções de manipulação de arquivos
├── tests/ # Testes unitários e de integração
│ ├── **init**.py
│ ├── test_scraper.py
│ └── test_generators.py
├── output/ # Resultados gerados
│ ├── html/ # Páginas HTML geradas
│ └── json/ # Dados exportados em JSON
├── docs/ # Documentação
│ ├── architecture.md
│ └── usage.md
├── requirements.txt # Dependências do projeto
├── LICENSE
├── README.md
├── .gitignore
└── cline_todo.md

# Apenas com username

curl -X POST "http://localhost:8000/api/v1/scrape/user" \
 -H "Content-Type: application/json" \
 -d '{"username": "ka_libretattoo"}'

# Apenas com URL

curl -X POST "http://localhost:8000/api/v1/scrape/user" \
 -H "Content-Type: application/json" \
 -d '{"url": "https://linktr.ee/ka_libretattoo"}'
