from io import Bytes<PERSON>
from typing import List, Optional
import requests
from colorthief import ColorThief

class ColorExtractor:
    """Handles color extraction from images"""

    def __init__(self):
        self.session = requests.Session()

    def download_image(self, image_url: str) -> Optional[BytesIO]:
        """Download image from URL and return as BytesIO object"""
        try:
            response = self.session.get(image_url)
            response.raise_for_status()
            return BytesIO(response.content)
        except requests.RequestException as e:
            return None

    def extract_colors(self, image_data: BytesIO, color_count: int = 3) -> List[str]:
        """Extract main colors from image data"""
        try:
            color_thief = ColorThief(image_data)
            palette = color_thief.get_palette(color_count=color_count)
            return [f"#{r:02x}{g:02x}{b:02x}" for r, g, b in palette]
        except Exception:
            return []

    def get_main_colors(self, image_url: str, color_count: int = 5) -> List[str]:
        """Get main colors from image URL"""
        image_data = self.download_image(image_url)
        if image_data is None:
            return []
        return self.extract_colors(image_data, color_count)
