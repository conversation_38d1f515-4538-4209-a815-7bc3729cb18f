from pydantic import Field
from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """Configurações da aplicação"""

    # API Configuration
    app_name: str = Field("LinkTree Scraper API", description="Nome da aplicação")
    app_version: str = Field("1.0.0", description="Versão da aplicação")
    debug: bool = Field(False, description="Modo debug")

    # Server Configuration
    host: str = Field("0.0.0.0", description="Host do servidor")
    port: int = Field(8000, description="Porta do servidor")

    # Rate Limiting
    rate_limit_requests: int = Field(100, description="Limite de requests por minuto")
    rate_limit_window: int = Field(
        60, description="Janela de tempo para rate limiting em segundos"
    )

    # Redis Configuration (for rate limiting)
    redis_url: Optional[str] = Field(None, description="URL do Redis")
    redis_host: str = Field("localhost", description="Host do Redis")
    redis_port: int = Field(6379, description="Porta do Redis")
    redis_db: int = Field(0, description="Database do Redis")
    redis_password: Optional[str] = Field(None, description="Senha do Redis")

    # Scraper Configuration
    scraper_timeout: int = Field(30, description="Timeout para scraping em segundos")
    scraper_max_retries: int = Field(3, description="Número máximo de tentativas")
    scraper_delay: float = Field(1.0, description="Delay entre requests em segundos")

    # CORS Configuration
    cors_origins: list = Field(["*"], description="Origens permitidas para CORS")
    cors_methods: list = Field(
        ["GET", "POST"], description="Métodos permitidos para CORS"
    )
    cors_headers: list = Field(["*"], description="Headers permitidos para CORS")

    model_config = {
        "env_file": ".env",
        "env_prefix": "LINKTREE_",
        "case_sensitive": False,
        "extra": "ignore",
    }


# Instância global das configurações
settings = Settings()
